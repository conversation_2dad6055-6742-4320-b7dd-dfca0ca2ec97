# Development Setup Guide

## Fixing Pylance Import Warnings

The Pylance warnings you're seeing are IDE-related and don't affect the actual functionality of the code. Here's how to resolve them:

### 1. VS Code Configuration

The project includes `.vscode/settings.json` with the correct Python interpreter path:

```json
{
    "python.defaultInterpreterPath": "./venv/Scripts/python.exe",
    "python.analysis.extraPaths": ["./venv/Lib/site-packages"]
}
```

### 2. Select the Correct Python Interpreter

In VS Code:
1. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
2. Type "Python: Select Interpreter"
3. Choose the interpreter from `./venv/Scripts/python.exe`

### 3. Restart VS Code

After selecting the interpreter, restart VS Code to ensure <PERSON><PERSON><PERSON> picks up the new configuration.

### 4. Alternative: Use PyCharm

If you prefer PyCharm:
1. Open the project
2. Go to File → Settings → Project → Python Interpreter
3. Select "Existing environment" and point to `./venv/Scripts/python.exe`

## Development Workflow

### 1. Environment Setup
```bash
# Activate virtual environment
venv\Scripts\activate

# Install dependencies
pip install -r requirements.txt

# Run setup verification
python setup_dev.py
```

### 2. Database Operations
```bash
# Create migrations
python manage.py makemigrations

# Apply migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser
```

### 3. Running the Development Server
```bash
# Start Django development server
python manage.py runserver

# Start Celery worker (in another terminal)
celery -A crosssell_project worker -l info

# Start Celery beat scheduler (in another terminal)
celery -A crosssell_project beat -l info
```

### 4. Code Quality Tools
```bash
# Format code with Black
black .

# Check code style with flake8
flake8 .

# Sort imports with isort
isort .
```

### 5. Testing
```bash
# Run tests
pytest

# Run tests with coverage
pytest --cov=.
```

## Database Configuration

### Development (SQLite)
The project is configured to use SQLite for development by default. This avoids PostgreSQL setup complexity during development.

### Production (PostgreSQL)
To use PostgreSQL:
1. Set `USE_POSTGRES=True` in your `.env` file
2. Configure PostgreSQL connection details in `.env`
3. Run migrations: `python manage.py migrate`

## Docker Development

### Using Docker Compose
```bash
# Start all services
docker-compose up

# Start in background
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

## Troubleshooting

### Pylance Import Warnings
- Ensure VS Code is using the correct Python interpreter
- Check that `.vscode/settings.json` exists with correct paths
- Restart VS Code after changing interpreter

### Database Issues
- For development, use SQLite (default configuration)
- For PostgreSQL, ensure the database server is running
- Check connection details in `.env` file

### Celery Issues
- Ensure Redis is running (use Docker: `docker run -d -p 6379:6379 redis`)
- Check Redis connection in settings

## Project Structure

```
ai-crosssell/
├── crosssell_project/      # Django project settings
├── products/               # Product management
├── recommendations/        # ML recommendation engine
├── payments/              # Paystack integration
├── data/                  # Dataset storage
├── models/                # Trained ML models
├── logs/                  # Application logs
├── venv/                  # Virtual environment
└── requirements.txt       # Dependencies
```

## Next Steps

1. ✅ Project setup complete
2. ✅ Database models created
3. 🔄 Data pipeline setup (next task)
4. ⏳ Association rule mining implementation
5. ⏳ API development
6. ⏳ Frontend implementation
7. ⏳ Paystack integration
8. ⏳ Testing and deployment
