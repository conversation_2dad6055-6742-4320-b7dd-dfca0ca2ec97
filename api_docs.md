# Cross-Selling API Documentation

## Overview

The Cross-Selling API provides comprehensive endpoints for product management, recommendation generation, and model training. Built with Django REST Framework, it offers a robust and scalable solution for e-commerce cross-selling functionality.

## Base URL

```
http://localhost:8000/api/
```

## Authentication

The API supports multiple authentication methods:

1. **Token Authentication** (Recommended for API clients)
   ```
   Authorization: Token your-token-here
   ```

2. **Session Authentication** (For web applications)
   - Uses Django's built-in session authentication

### Getting a Token

```http
POST /api/auth/token/
Content-Type: application/json

{
    "username": "your-username",
    "password": "your-password"
}
```

## Products API

### Categories

#### List Categories
```http
GET /api/categories/
```

#### Get Category Details
```http
GET /api/categories/{slug}/
```

#### Get Products in Category
```http
GET /api/categories/{slug}/products/
```

### Products

#### List Products
```http
GET /api/products/
```

**Query Parameters:**
- `search`: Search in name, description, SKU
- `category`: Filter by category slug
- `is_featured`: Filter featured products
- `ordering`: Sort by fields (name, price, created_at, popularity_score, rating_average)

#### Get Product Details
```http
GET /api/products/{slug}/
```

#### Advanced Product Search
```http
GET /api/products/search/
```

**Query Parameters:**
- `q`: Search query
- `category`: Category slug
- `min_price`: Minimum price
- `max_price`: Maximum price
- `in_stock`: Filter in-stock products (true/false)
- `featured`: Filter featured products (true/false)
- `sort_by`: Sort field

#### Get Featured Products
```http
GET /api/products/featured/
```

#### Get Trending Products
```http
GET /api/products/trending/
```

**Query Parameters:**
- `days`: Number of days to look back (default: 7)

#### Get Product Reviews
```http
GET /api/products/{slug}/reviews/
```

#### Add Product Review
```http
POST /api/products/{slug}/add_review/
Authorization: Token your-token-here
Content-Type: application/json

{
    "rating": 5,
    "title": "Great product!",
    "comment": "I really enjoyed this product..."
}
```

### Shopping Cart

#### Get Current Cart
```http
GET /api/cart/current/
Authorization: Token your-token-here
```

#### Add Item to Cart
```http
POST /api/cart/add_item/
Authorization: Token your-token-here
Content-Type: application/json

{
    "product_id": "product-uuid",
    "quantity": 2
}
```

#### Update Cart Item
```http
PUT /api/cart/update_item/
Authorization: Token your-token-here
Content-Type: application/json

{
    "product_id": "product-uuid",
    "quantity": 3
}
```

#### Remove Cart Item
```http
DELETE /api/cart/remove_item/
Authorization: Token your-token-here
Content-Type: application/json

{
    "product_id": "product-uuid"
}
```

#### Bulk Update Cart
```http
POST /api/cart/bulk_update/
Authorization: Token your-token-here
Content-Type: application/json

{
    "items": [
        {"product_id": "uuid1", "quantity": "2"},
        {"product_id": "uuid2", "quantity": "1"}
    ]
}
```

#### Clear Cart
```http
DELETE /api/cart/clear/
Authorization: Token your-token-here
```

### Orders

#### List Customer Orders
```http
GET /api/orders/
Authorization: Token your-token-here
```

#### Get Order Details
```http
GET /api/orders/{id}/
Authorization: Token your-token-here
```

#### Get Order Items
```http
GET /api/orders/{id}/items/
Authorization: Token your-token-here
```

### Customer Profile

#### Get Profile
```http
GET /api/customers/profile/
Authorization: Token your-token-here
```

#### Update Profile
```http
PUT /api/customers/update_profile/
Authorization: Token your-token-here
Content-Type: application/json

{
    "phone": "+1234567890",
    "address_line_1": "123 Main St",
    "city": "New York",
    "state": "NY",
    "postal_code": "10001"
}
```

#### Get Customer Orders
```http
GET /api/customers/orders/
Authorization: Token your-token-here
```

#### Get Purchase History
```http
GET /api/customers/purchase_history/
Authorization: Token your-token-here
```

## Recommendations API

### Get Recommendations

#### Product-based Recommendations
```http
GET /recommendations/api/engine/product_recommendations/
```

**Query Parameters:**
- `product_ids`: List of product UUIDs
- `max_recommendations`: Maximum number of recommendations (default: 5)
- `min_confidence`: Minimum confidence threshold (default: 0.1)
- `min_lift`: Minimum lift threshold (default: 1.0)

#### Customer Recommendations
```http
GET /recommendations/api/engine/customer_recommendations/
Authorization: Token your-token-here
```

**Query Parameters:**
- `customer_id`: Customer UUID
- `max_recommendations`: Maximum recommendations (default: 5)
- `days_lookback`: Days to look back for history (default: 30)

#### Cart-based Recommendations
```http
POST /recommendations/api/engine/cart_recommendations/
Content-Type: application/json

{
    "cart_items": [
        {"product_id": "uuid1", "quantity": 2},
        {"product_id": "uuid2", "quantity": 1}
    ],
    "max_recommendations": 5
}
```

#### Bundle Recommendations
```http
GET /recommendations/api/engine/bundles/
```

**Query Parameters:**
- `product_ids`: List of product UUIDs
- `max_bundles`: Maximum bundles (default: 3)
- `min_confidence`: Minimum confidence (default: 0.3)

#### Advanced Recommendation Request
```http
POST /recommendations/api/engine/get_recommendations/
Content-Type: application/json

{
    "product_ids": ["uuid1", "uuid2"],
    "customer_id": "customer-uuid",
    "recommendation_type": "cross_sell",
    "max_recommendations": 5,
    "min_confidence": 0.2,
    "min_lift": 1.5,
    "exclude_products": ["uuid3"]
}
```

### Track Interactions

#### Track Recommendation Interaction
```http
POST /recommendations/api/engine/track_interaction/
Content-Type: application/json

{
    "source_product_id": "source-uuid",
    "recommended_product_id": "recommended-uuid",
    "interaction_type": "click"
}
```

**Interaction Types:**
- `view`: User viewed the recommendation
- `click`: User clicked on the recommendation
- `purchase`: User purchased the recommended product

### Analytics

#### Get Recommendation Analytics
```http
GET /recommendations/api/engine/analytics/
Authorization: Token your-token-here
```

**Query Parameters:**
- `days`: Number of days to analyze (default: 30)

### Association Rules

#### List Association Rules
```http
GET /recommendations/api/association-rules/
```

#### Get Top Rules
```http
GET /recommendations/api/association-rules/top_rules/
```

**Query Parameters:**
- `limit`: Number of rules (default: 10)
- `min_confidence`: Minimum confidence (default: 0.3)
- `min_lift`: Minimum lift (default: 1.5)

#### Get Rules by Product
```http
GET /recommendations/api/association-rules/by_product/
```

**Query Parameters:**
- `product_id`: Product UUID (required)

### Model Training

#### Start Model Training
```http
POST /recommendations/api/training/train_model/
Authorization: Token your-token-here
Content-Type: application/json

{
    "min_support": 0.01,
    "min_confidence": 0.3,
    "max_itemsets": 500,
    "data_source": "processed"
}
```

#### Get Training Status
```http
GET /recommendations/api/training/training_status/
Authorization: Token your-token-here
```

**Query Parameters:**
- `task_id`: Training task ID (required)

#### Get Training History
```http
GET /recommendations/api/training/training_history/
Authorization: Token your-token-here
```

#### Get Model Performance
```http
GET /recommendations/api/training/model_performance/
Authorization: Token your-token-here
```

#### Retrain Model
```http
POST /recommendations/api/training/retrain_model/
Authorization: Token your-token-here
Content-Type: application/json

{
    "force_retrain": false
}
```

## Response Formats

### Success Response
```json
{
    "data": {...},
    "status": "success"
}
```

### Error Response
```json
{
    "error": "Error message",
    "status": "error",
    "details": {...}
}
```

### Paginated Response
```json
{
    "count": 100,
    "next": "http://api.example.org/accounts/?page=4",
    "previous": "http://api.example.org/accounts/?page=2",
    "results": [...]
}
```

## Rate Limiting

The API implements rate limiting to ensure fair usage:

- **Authenticated users**: 1000 requests per hour
- **Anonymous users**: 100 requests per hour
- **Model training**: 10 requests per hour

## Error Codes

- `400 Bad Request`: Invalid request parameters
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## SDKs and Examples

### Python Example
```python
import requests

# Get token
response = requests.post('http://localhost:8000/api/auth/token/', {
    'username': 'your-username',
    'password': 'your-password'
})
token = response.json()['token']

# Get recommendations
headers = {'Authorization': f'Token {token}'}
response = requests.get(
    'http://localhost:8000/recommendations/api/engine/product_recommendations/',
    params={'product_ids': ['uuid1', 'uuid2']},
    headers=headers
)
recommendations = response.json()
```

### JavaScript Example
```javascript
// Get recommendations
fetch('/recommendations/api/engine/product_recommendations/?product_ids=uuid1,uuid2')
    .then(response => response.json())
    .then(data => {
        console.log('Recommendations:', data.recommendations);
    });
```

## Support

For API support and questions:
- Documentation: `/api/docs/`
- Browsable API: `/api/`
- Issues: Create an issue in the project repository
