#!/usr/bin/env python
"""
Create comprehensive sample data for recommendation testing
"""
import os
import django
from decimal import Decimal
import random
from datetime import datetime, timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'crosssell_project.settings')
django.setup()

from django.contrib.auth.models import User
from products.models import Category, Product, Customer, Order, OrderItem, ProductView, ProductReview
from django.utils import timezone

def create_categories():
    """Create sample categories"""
    print("📂 Creating Categories...")
    
    categories_data = [
        {'name': 'Electronics', 'slug': 'electronics', 'description': 'Electronic products and gadgets'},
        {'name': 'Computers', 'slug': 'computers', 'description': 'Laptops, desktops, and accessories'},
        {'name': 'Mobile Phones', 'slug': 'mobile-phones', 'description': 'Smartphones and accessories'},
        {'name': 'Audio', 'slug': 'audio', 'description': 'Headphones, speakers, and audio equipment'},
        {'name': 'Gaming', 'slug': 'gaming', 'description': 'Gaming consoles and accessories'},
    ]
    
    created_categories = []
    for cat_data in categories_data:
        category, created = Category.objects.get_or_create(
            slug=cat_data['slug'],
            defaults=cat_data
        )
        if created:
            print(f"   ✅ Created: {category.name}")
        created_categories.append(category)
    
    return created_categories

def create_products(categories):
    """Create sample products"""
    print("\n📦 Creating Products...")
    
    products_data = [
        # Electronics
        {'name': 'iPhone 15 Pro', 'slug': 'iphone-15-pro', 'price': 999.99, 'category': 'electronics', 'sku': 'PHONE001'},
        {'name': 'Samsung Galaxy S24', 'slug': 'samsung-galaxy-s24', 'price': 899.99, 'category': 'electronics', 'sku': 'PHONE002'},
        {'name': 'Google Pixel 8', 'slug': 'google-pixel-8', 'price': 699.99, 'category': 'electronics', 'sku': 'PHONE003'},
        
        # Computers
        {'name': 'MacBook Pro M3', 'slug': 'macbook-pro-m3', 'price': 1999.99, 'category': 'computers', 'sku': 'LAPTOP001'},
        {'name': 'Dell XPS 13', 'slug': 'dell-xps-13', 'price': 1299.99, 'category': 'computers', 'sku': 'LAPTOP002'},
        {'name': 'HP Spectre x360', 'slug': 'hp-spectre-x360', 'price': 1199.99, 'category': 'computers', 'sku': 'LAPTOP003'},
        
        # Audio
        {'name': 'AirPods Pro 2', 'slug': 'airpods-pro-2', 'price': 249.99, 'category': 'audio', 'sku': 'AUDIO001'},
        {'name': 'Sony WH-1000XM5', 'slug': 'sony-wh-1000xm5', 'price': 399.99, 'category': 'audio', 'sku': 'AUDIO002'},
        {'name': 'Bose QuietComfort', 'slug': 'bose-quietcomfort', 'price': 329.99, 'category': 'audio', 'sku': 'AUDIO003'},
        
        # Gaming
        {'name': 'PlayStation 5', 'slug': 'playstation-5', 'price': 499.99, 'category': 'gaming', 'sku': 'GAME001'},
        {'name': 'Xbox Series X', 'slug': 'xbox-series-x', 'price': 499.99, 'category': 'gaming', 'sku': 'GAME002'},
        {'name': 'Nintendo Switch OLED', 'slug': 'nintendo-switch-oled', 'price': 349.99, 'category': 'gaming', 'sku': 'GAME003'},
        
        # Accessories
        {'name': 'iPhone Case', 'slug': 'iphone-case', 'price': 29.99, 'category': 'electronics', 'sku': 'ACC001'},
        {'name': 'Laptop Stand', 'slug': 'laptop-stand', 'price': 49.99, 'category': 'computers', 'sku': 'ACC002'},
        {'name': 'Wireless Charger', 'slug': 'wireless-charger', 'price': 39.99, 'category': 'electronics', 'sku': 'ACC003'},
    ]
    
    category_map = {cat.slug: cat for cat in categories}
    created_products = []
    
    for prod_data in products_data:
        category = category_map.get(prod_data['category'])
        if category:
            product, created = Product.objects.get_or_create(
                sku=prod_data['sku'],
                defaults={
                    'name': prod_data['name'],
                    'slug': prod_data['slug'],
                    'description': f"High-quality {prod_data['name']} with excellent features.",
                    'short_description': f"Premium {prod_data['name']}",
                    'price': Decimal(str(prod_data['price'])),
                    'category': category,
                    'stock_quantity': random.randint(10, 100),
                    'is_featured': random.choice([True, False]),
                    'is_active': True,
                }
            )
            if created:
                print(f"   ✅ Created: {product.name}")
            created_products.append(product)
    
    return created_products

def create_customers():
    """Create sample customers"""
    print("\n👥 Creating Customers...")
    
    customers_data = [
        {'username': 'alice_smith', 'email': '<EMAIL>', 'first_name': 'Alice', 'last_name': 'Smith'},
        {'username': 'bob_jones', 'email': '<EMAIL>', 'first_name': 'Bob', 'last_name': 'Jones'},
        {'username': 'carol_white', 'email': '<EMAIL>', 'first_name': 'Carol', 'last_name': 'White'},
        {'username': 'david_brown', 'email': '<EMAIL>', 'first_name': 'David', 'last_name': 'Brown'},
        {'username': 'eve_davis', 'email': '<EMAIL>', 'first_name': 'Eve', 'last_name': 'Davis'},
    ]
    
    created_customers = []
    for cust_data in customers_data:
        user, created = User.objects.get_or_create(
            username=cust_data['username'],
            defaults={
                'email': cust_data['email'],
                'first_name': cust_data['first_name'],
                'last_name': cust_data['last_name'],
            }
        )
        if created:
            user.set_password('password123')
            user.save()
        
        customer, created = Customer.objects.get_or_create(
            user=user,
            defaults={
                'phone': f'+1555{random.randint(1000000, 9999999)}',
                'address_line_1': f'{random.randint(100, 999)} Main St',
                'city': random.choice(['New York', 'Los Angeles', 'Chicago', 'Houston', 'Phoenix']),
                'state': random.choice(['NY', 'CA', 'IL', 'TX', 'AZ']),
                'postal_code': f'{random.randint(10000, 99999)}',
                'country': 'US',
            }
        )
        if created:
            print(f"   ✅ Created: {customer.user.get_full_name()}")
        created_customers.append(customer)
    
    return created_customers

def create_orders_and_transactions(customers, products):
    """Create sample orders and transactions"""
    print("\n🛒 Creating Orders and Transactions...")
    
    # Define common product combinations (for association rules)
    common_combinations = [
        ['iPhone 15 Pro', 'AirPods Pro 2', 'iPhone Case'],
        ['MacBook Pro M3', 'Laptop Stand', 'Wireless Charger'],
        ['PlayStation 5', 'Sony WH-1000XM5'],
        ['Samsung Galaxy S24', 'Wireless Charger'],
        ['Dell XPS 13', 'Laptop Stand'],
        ['Nintendo Switch OLED', 'Bose QuietComfort'],
        ['Google Pixel 8', 'iPhone Case'],  # Cross-brand accessories
        ['Xbox Series X', 'Sony WH-1000XM5'],
    ]
    
    product_map = {prod.name: prod for prod in products}
    
    order_count = 0
    for customer in customers:
        # Each customer makes 2-5 orders
        num_orders = random.randint(2, 5)
        
        for _ in range(num_orders):
            # Create order
            order = Order.objects.create(
                customer=customer,
                order_number=f'ORD{random.randint(100000, 999999)}',
                status='completed',
                subtotal=Decimal('0.00'),
                tax_amount=Decimal('0.00'),
                total_amount=Decimal('0.00'),
                created_at=timezone.now() - timedelta(days=random.randint(1, 90))
            )
            
            # Choose products for this order
            if random.random() < 0.7:  # 70% chance of common combination
                combination = random.choice(common_combinations)
                selected_products = []
                for prod_name in combination:
                    if prod_name in product_map:
                        selected_products.append(product_map[prod_name])
            else:  # 30% chance of random products
                selected_products = random.sample(products, random.randint(1, 3))
            
            total = Decimal('0.00')
            for product in selected_products:
                quantity = random.randint(1, 2)
                unit_price = product.price
                
                OrderItem.objects.create(
                    order=order,
                    product=product,
                    quantity=quantity,
                    unit_price=unit_price
                )
                
                total += unit_price * quantity
            
            # Update order totals
            order.subtotal = total
            order.tax_amount = total * Decimal('0.08')  # 8% tax
            order.total_amount = order.subtotal + order.tax_amount
            order.save()
            
            order_count += 1
    
    print(f"   ✅ Created {order_count} orders with transaction data")

def create_product_views(customers, products):
    """Create sample product views"""
    print("\n👀 Creating Product Views...")
    
    view_count = 0
    for customer in customers:
        # Each customer views 10-20 products
        viewed_products = random.sample(products, random.randint(10, min(20, len(products))))
        
        for product in viewed_products:
            # Multiple views per product
            for _ in range(random.randint(1, 3)):
                ProductView.objects.create(
                    product=product,
                    customer=customer,
                    session_key=f'session_{customer.id}_{random.randint(1000, 9999)}',
                    ip_address=f'192.168.1.{random.randint(1, 254)}',
                    user_agent='Mozilla/5.0 (Test Browser)',
                    viewed_at=timezone.now() - timedelta(days=random.randint(1, 30))
                )
                view_count += 1
    
    print(f"   ✅ Created {view_count} product views")

def create_reviews(customers, products):
    """Create sample product reviews"""
    print("\n⭐ Creating Product Reviews...")
    
    review_count = 0
    for customer in customers:
        # Each customer reviews 2-5 products
        reviewed_products = random.sample(products, random.randint(2, 5))
        
        for product in reviewed_products:
            rating = random.choices([1, 2, 3, 4, 5], weights=[5, 10, 15, 35, 35])[0]
            
            review_texts = {
                5: ["Excellent product!", "Love it!", "Highly recommended!", "Perfect quality!"],
                4: ["Very good", "Great value", "Satisfied with purchase", "Good quality"],
                3: ["It's okay", "Average product", "Does the job", "Not bad"],
                2: ["Could be better", "Some issues", "Not impressed", "Below expectations"],
                1: ["Poor quality", "Disappointed", "Would not recommend", "Waste of money"]
            }
            
            ProductReview.objects.create(
                product=product,
                customer=customer,
                rating=rating,
                title=f"Review for {product.name}",
                review_text=random.choice(review_texts[rating]),
                is_verified_purchase=True,
                is_approved=True,
                created_at=timezone.now() - timedelta(days=random.randint(1, 60))
            )
            review_count += 1
    
    print(f"   ✅ Created {review_count} product reviews")

def main():
    """Main function to create all sample data"""
    print("🚀 Creating Comprehensive Sample Data")
    print("=" * 50)
    
    # Create data in order
    categories = create_categories()
    products = create_products(categories)
    customers = create_customers()
    create_orders_and_transactions(customers, products)
    create_product_views(customers, products)
    create_reviews(customers, products)
    
    print("\n" + "=" * 50)
    print("✅ Sample Data Creation Complete!")
    print(f"📊 Summary:")
    print(f"   - Categories: {Category.objects.count()}")
    print(f"   - Products: {Product.objects.count()}")
    print(f"   - Customers: {Customer.objects.count()}")
    print(f"   - Orders: {Order.objects.count()}")
    print(f"   - Order Items: {OrderItem.objects.count()}")
    print(f"   - Product Views: {ProductView.objects.count()}")
    print(f"   - Reviews: {ProductReview.objects.count()}")
    
    print("\n🎯 Ready for recommendation training!")

if __name__ == '__main__':
    main()
