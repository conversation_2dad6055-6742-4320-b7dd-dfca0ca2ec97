/* auto-generated by NAPI-RS */
/* eslint-disable */
export declare class Scanner {
  constructor(opts: ScannerOptions)
  scan(): Array<string>
  scanFiles(input: Array<ChangedContent>): Array<string>
  getCandidatesWithPositions(input: ChangedContent): Array<CandidateWithPosition>
  get files(): Array<string>
  get globs(): Array<GlobEntry>
  get normalizedSources(): Array<GlobEntry>
}

export interface CandidateWithPosition {
  /** The candidate string */
  candidate: string
  /** The position of the candidate inside the content file */
  position: number
}

export interface ChangedContent {
  /** File path to the changed file */
  file?: string
  /** Contents of the changed file */
  content?: string
  /** File extension */
  extension: string
}

export interface GlobEntry {
  /** Base path of the glob */
  base: string
  /** Glob pattern */
  pattern: string
}

export interface ScannerOptions {
  /** Glob sources */
  sources?: Array<SourceEntry>
}

export interface SourceEntry {
  /** Base path of the glob */
  base: string
  /** Glob pattern */
  pattern: string
  /** Negated flag */
  negated: boolean
}
