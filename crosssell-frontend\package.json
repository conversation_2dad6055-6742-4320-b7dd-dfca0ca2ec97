{"name": "crosssell-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "devDependencies": {"vite": "^7.0.3"}, "dependencies": {"@heroicons/react": "^2.2.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "autoprefixer": "^10.4.21", "axios": "^1.10.0", "postcss": "^8.5.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.3", "tailwindcss": "^4.1.11"}}