import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './styles.css';

// Navigation Component
const Navbar = () => (
  <nav className="navbar">
    <div className="container">
      <a href="/" className="navbar-brand">CrossSell</a>
      <ul className="navbar-nav">
        <li><a href="/products">Products</a></li>
        <li><a href="/cart">Cart</a></li>
        <li><a href="/login" className="btn btn-primary">Login</a></li>
      </ul>
    </div>
  </nav>
);

// Home Page Component
const HomePage = () => (
  <div>
    <section className="hero">
      <div className="container">
        <h1>CrossSell E-commerce</h1>
        <p>AI-Powered Product Recommendations</p>
        <div className="card" style={{ maxWidth: '500px', margin: '2rem auto' }}>
          <p className="text-green-600 font-semibold mb-2">
            ✅ React is working!
          </p>
          <p className="text-blue-600 font-semibold mb-2">
            ✅ Frontend is working!
          </p>
          <p className="text-blue-600 font-semibold">
            ✅ React Router is working!
          </p>
        </div>
        <div className="hero-buttons">
          <a href="/products" className="btn btn-primary">
            Browse Products
          </a>
          <a href="/login" className="btn btn-secondary">
            Get Started
          </a>
        </div>
      </div>
    </section>

    <section style={{ padding: '4rem 0' }}>
      <div className="container">
        <h2 className="text-center text-3xl font-bold mb-8">Features</h2>
        <div className="product-grid">
          <div className="card text-center">
            <h3 className="text-xl font-semibold mb-4">🤖 AI Recommendations</h3>
            <p className="text-gray-600">Smart product suggestions powered by machine learning algorithms.</p>
          </div>
          <div className="card text-center">
            <h3 className="text-xl font-semibold mb-4">🛒 Smart Cart</h3>
            <p className="text-gray-600">Intelligent shopping cart with cross-sell suggestions.</p>
          </div>
          <div className="card text-center">
            <h3 className="text-xl font-semibold mb-4">📊 Analytics</h3>
            <p className="text-gray-600">Detailed insights into customer behavior and preferences.</p>
          </div>
        </div>
      </div>
    </section>
  </div>
);

// Products Page Component
const ProductsPage = () => (
  <div>
    <Navbar />
    <div className="container" style={{ padding: '2rem 0', minHeight: '80vh' }}>
      <h1 className="text-3xl font-bold mb-4">Products</h1>
      <p className="text-gray-600 mb-8">Discover our amazing products with AI-powered recommendations</p>

      <div className="product-grid">
        <div className="product-card">
          <div className="product-image">Product Image</div>
          <div className="product-info">
            <h3 className="product-title">Sample Product 1</h3>
            <p className="product-description">This is a sample product description.</p>
            <div className="product-price">$99.99</div>
            <button className="btn btn-primary w-full">Add to Cart</button>
          </div>
        </div>

        <div className="product-card">
          <div className="product-image">Product Image</div>
          <div className="product-info">
            <h3 className="product-title">Sample Product 2</h3>
            <p className="product-description">Another sample product description.</p>
            <div className="product-price">$149.99</div>
            <button className="btn btn-primary w-full">Add to Cart</button>
          </div>
        </div>

        <div className="product-card">
          <div className="product-image">Product Image</div>
          <div className="product-info">
            <h3 className="product-title">Sample Product 3</h3>
            <p className="product-description">Yet another sample product.</p>
            <div className="product-price">$79.99</div>
            <button className="btn btn-primary w-full">Add to Cart</button>
          </div>
        </div>
      </div>

      <div className="alert alert-info mt-8">
        <strong>🤖 AI Recommendation:</strong> Products are being loaded from the backend API.
        This is a demo showing the frontend structure.
      </div>
    </div>
  </div>
);

// Login Page Component
const LoginPage = () => (
  <div>
    <Navbar />
    <div className="container" style={{ padding: '2rem 0', minHeight: '80vh' }}>
      <div style={{ maxWidth: '400px', margin: '0 auto' }}>
        <div className="card">
          <h1 className="text-2xl font-bold mb-4 text-center">Login</h1>

          <form>
            <div className="form-group">
              <label className="form-label">Username</label>
              <input type="text" className="form-input" placeholder="Enter username" />
            </div>

            <div className="form-group">
              <label className="form-label">Password</label>
              <input type="password" className="form-input" placeholder="Enter password" />
            </div>

            <button type="submit" className="btn btn-primary w-full">
              Sign In
            </button>
          </form>

          <div className="alert alert-info mt-4">
            <strong>Demo Credentials:</strong><br />
            Username: testuser<br />
            Password: testpass123
          </div>
        </div>
      </div>
    </div>
  </div>
);

// Cart Page Component
const CartPage = () => (
  <div>
    <Navbar />
    <div className="container" style={{ padding: '2rem 0', minHeight: '80vh' }}>
      <h1 className="text-3xl font-bold mb-4">Shopping Cart</h1>
      <div className="alert alert-info">
        <strong>🛒 Cart functionality:</strong> Cart features will be connected to the backend API.
        This shows the frontend structure for cart management.
      </div>
    </div>
  </div>
);

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/products" element={<ProductsPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/cart" element={<CartPage />} />
      </Routes>
    </Router>
  );
}

export default App;
