import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import './styles.css';

// API Service
const API_BASE_URL = 'http://127.0.0.1:8000';

const apiService = {
  // Products
  getProducts: async () => {
    const response = await fetch(`${API_BASE_URL}/api/products/`);
    return response.json();
  },

  getCategories: async () => {
    const response = await fetch(`${API_BASE_URL}/api/categories/`);
    return response.json();
  },

  getFeaturedProducts: async () => {
    const response = await fetch(`${API_BASE_URL}/api/products/featured/`);
    return response.json();
  },

  // Authentication
  login: async (credentials) => {
    const response = await fetch(`${API_BASE_URL}/api/auth/token/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(credentials),
    });
    return response.json();
  },

  // Recommendations
  getRecommendations: async () => {
    const response = await fetch(`${API_BASE_URL}/recommendations/api/product-recommendations/`);
    return response.json();
  },

  getAssociationRules: async () => {
    const response = await fetch(`${API_BASE_URL}/recommendations/api/association-rules/`);
    return response.json();
  }
};

// Navigation Component
const Navbar = () => (
  <nav className="navbar">
    <div className="container">
      <a href="/" className="navbar-brand">CrossSell</a>
      <ul className="navbar-nav">
        <li><a href="/products">Products</a></li>
        <li><a href="/recommendations">AI Recommendations</a></li>
        <li><a href="/cart">Cart</a></li>
        <li><a href="/login" className="btn btn-primary">Login</a></li>
      </ul>
    </div>
  </nav>
);

// Home Page Component with Real Data
const HomePage = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [apiStatus, setApiStatus] = useState({
    backend: false,
    products: false,
    recommendations: false
  });

  useEffect(() => {
    fetchHomeData();
  }, []);

  const fetchHomeData = async () => {
    try {
      // Test backend connection
      const productsResponse = await apiService.getProducts();
      setApiStatus(prev => ({ ...prev, backend: true, products: true }));

      // Get featured products
      try {
        const featuredResponse = await apiService.getFeaturedProducts();
        setFeaturedProducts(featuredResponse.results || []);
      } catch (err) {
        // Fallback to regular products
        setFeaturedProducts(productsResponse.results?.slice(0, 4) || []);
      }

      // Get recommendations
      try {
        const recsResponse = await apiService.getRecommendations();
        setRecommendations(recsResponse.results?.slice(0, 3) || []);
        setApiStatus(prev => ({ ...prev, recommendations: true }));
      } catch (err) {
        console.log('Recommendations not available:', err);
      }

    } catch (error) {
      console.error('Failed to fetch data:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <section className="hero">
        <div className="container">
          <h1>CrossSell E-commerce</h1>
          <p>AI-Powered Product Recommendations</p>

          {/* API Status Card */}
          <div className="card" style={{ maxWidth: '600px', margin: '2rem auto' }}>
            <h3 className="text-lg font-semibold mb-4">🔗 Backend Connection Status</h3>
            <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(150px, 1fr))', gap: '1rem' }}>
              <div className="text-center">
                <p className={apiStatus.backend ? "text-green-600 font-semibold" : "text-red-600 font-semibold"}>
                  {apiStatus.backend ? "✅" : "❌"} Django API
                </p>
              </div>
              <div className="text-center">
                <p className={apiStatus.products ? "text-green-600 font-semibold" : "text-red-600 font-semibold"}>
                  {apiStatus.products ? "✅" : "❌"} Products
                </p>
              </div>
              <div className="text-center">
                <p className={apiStatus.recommendations ? "text-green-600 font-semibold" : "text-red-600 font-semibold"}>
                  {apiStatus.recommendations ? "✅" : "❌"} AI Recommendations
                </p>
              </div>
            </div>
          </div>

          <div className="hero-buttons">
            <a href="/products" className="btn btn-primary">
              Browse Products
            </a>
            <a href="/login" className="btn btn-secondary">
              Get Started
            </a>
          </div>
        </div>
      </section>

      {/* Featured Products Section */}
      {featuredProducts.length > 0 && (
        <section style={{ padding: '4rem 0', backgroundColor: 'white' }}>
          <div className="container">
            <h2 className="text-center text-3xl font-bold mb-8">Featured Products</h2>
            <div className="product-grid">
              {featuredProducts.map((product) => (
                <div key={product.id} className="product-card">
                  <div className="product-image">
                    {product.primary_image ? (
                      <img src={product.primary_image} alt={product.name} style={{ width: '100%', height: '100%', objectFit: 'cover' }} />
                    ) : (
                      `📦 ${product.name}`
                    )}
                  </div>
                  <div className="product-info">
                    <h3 className="product-title">{product.name}</h3>
                    <p className="product-description">{product.description || 'No description available'}</p>
                    <div className="product-price">${product.price}</div>
                    <button className="btn btn-primary w-full">Add to Cart</button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* AI Recommendations Section */}
      {recommendations.length > 0 && (
        <section style={{ padding: '4rem 0', backgroundColor: '#f8fafc' }}>
          <div className="container">
            <h2 className="text-center text-3xl font-bold mb-4">🤖 AI-Powered Recommendations</h2>
            <p className="text-center text-gray-600 mb-8">Based on customer purchase patterns and machine learning</p>
            <div className="product-grid">
              {recommendations.map((rec, index) => (
                <div key={index} className="card text-center">
                  <h3 className="text-lg font-semibold mb-2">
                    {rec.source_product?.name} → {rec.recommended_product?.name}
                  </h3>
                  <div className="text-sm text-gray-600 mb-2">
                    <p>Confidence: {(rec.confidence * 100).toFixed(1)}%</p>
                    <p>Score: {rec.score.toFixed(2)}</p>
                  </div>
                  <span className="recommendation-badge">
                    {rec.recommendation_type}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </section>
      )}

      {/* Features Section */}
      <section style={{ padding: '4rem 0' }}>
        <div className="container">
          <h2 className="text-center text-3xl font-bold mb-8">Platform Features</h2>
          <div className="product-grid">
            <div className="card text-center">
              <h3 className="text-xl font-semibold mb-4">🤖 AI Recommendations</h3>
              <p className="text-gray-600">Smart product suggestions powered by association rule mining and machine learning algorithms.</p>
            </div>
            <div className="card text-center">
              <h3 className="text-xl font-semibold mb-4">🛒 Smart Cart</h3>
              <p className="text-gray-600">Intelligent shopping cart with real-time cross-sell suggestions based on current items.</p>
            </div>
            <div className="card text-center">
              <h3 className="text-xl font-semibold mb-4">📊 Analytics</h3>
              <p className="text-gray-600">Detailed insights into customer behavior, purchase patterns, and recommendation performance.</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

// Products Page Component with Real Data
const ProductsPage = () => {
  const [products, setProducts] = useState([]);
  const [categories, setCategories] = useState([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchProductsData();
  }, []);

  const fetchProductsData = async () => {
    try {
      const [productsResponse, categoriesResponse] = await Promise.all([
        apiService.getProducts(),
        apiService.getCategories()
      ]);

      setProducts(productsResponse.results || []);
      setCategories(categoriesResponse.results || []);
    } catch (error) {
      console.error('Failed to fetch products:', error);
    } finally {
      setLoading(false);
    }
  };

  const filteredProducts = products.filter(product => {
    const matchesCategory = !selectedCategory || product.category_name === selectedCategory;
    const matchesSearch = !searchTerm ||
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  return (
    <div>
      <Navbar />
      <div className="container" style={{ padding: '2rem 0', minHeight: '80vh' }}>
        <h1 className="text-3xl font-bold mb-4">Products</h1>
        <p className="text-gray-600 mb-8">
          Discover our amazing products with AI-powered recommendations ({products.length} total products)
        </p>

        {/* Filters */}
        <div className="card mb-8">
          <div style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))', gap: '1rem' }}>
            <div className="form-group">
              <label className="form-label">Search Products</label>
              <input
                type="text"
                className="form-input"
                placeholder="Search by name or description..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="form-group">
              <label className="form-label">Filter by Category</label>
              <select
                className="form-input"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.name}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {loading ? (
          <div className="loading">
            <div className="spinner"></div>
            <span style={{ marginLeft: '1rem' }}>Loading products...</span>
          </div>
        ) : (
          <>
            <div className="product-grid">
              {filteredProducts.map((product) => (
                <div key={product.id} className="product-card">
                  <div className="product-image">
                    {product.primary_image ? (
                      <img
                        src={product.primary_image}
                        alt={product.name}
                        style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                      />
                    ) : (
                      <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
                        📦 {product.name}
                      </div>
                    )}
                  </div>
                  <div className="product-info">
                    <div style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.5rem' }}>
                      {product.category_name}
                    </div>
                    <h3 className="product-title">{product.name}</h3>
                    <p className="product-description">
                      {product.description || 'No description available'}
                    </p>
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '1rem' }}>
                      <div className="product-price">${product.price}</div>
                      {product.is_featured && (
                        <span style={{
                          backgroundColor: '#fbbf24',
                          color: 'white',
                          padding: '0.25rem 0.5rem',
                          borderRadius: '0.25rem',
                          fontSize: '0.75rem',
                          fontWeight: '500'
                        }}>
                          Featured
                        </span>
                      )}
                    </div>
                    <button className="btn btn-primary w-full">
                      Add to Cart
                    </button>
                  </div>
                </div>
              ))}
            </div>

            {filteredProducts.length === 0 && (
              <div className="text-center py-8">
                <p className="text-gray-600">No products found matching your criteria.</p>
              </div>
            )}
          </>
        )}

        <div className="alert alert-success mt-8">
          <strong>✅ Real Data Connected:</strong> Products are being loaded from the Django backend API.
          This shows live data from our PostgreSQL database with {products.length} products across {categories.length} categories.
        </div>
      </div>
    </div>
  );
};

// Login Page Component with Real Authentication
const LoginPage = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const handleChange = (e) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const response = await apiService.login(formData);

      if (response.token) {
        localStorage.setItem('authToken', response.token);
        setIsLoggedIn(true);
        setMessage('✅ Login successful! Token saved to localStorage.');
      } else {
        setMessage('❌ Login failed: ' + (response.detail || 'Invalid credentials'));
      }
    } catch (error) {
      setMessage('❌ Login error: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('authToken');
    setIsLoggedIn(false);
    setMessage('Logged out successfully');
  };

  return (
    <div>
      <Navbar />
      <div className="container" style={{ padding: '2rem 0', minHeight: '80vh' }}>
        <div style={{ maxWidth: '400px', margin: '0 auto' }}>
          <div className="card">
            <h1 className="text-2xl font-bold mb-4 text-center">
              {isLoggedIn ? 'Welcome!' : 'Login'}
            </h1>

            {message && (
              <div className={`alert ${message.includes('✅') ? 'alert-success' : 'alert-error'} mb-4`}>
                {message}
              </div>
            )}

            {isLoggedIn ? (
              <div className="text-center">
                <p className="mb-4">You are successfully logged in!</p>
                <p className="text-sm text-gray-600 mb-4">
                  Token: {localStorage.getItem('authToken')?.substring(0, 20)}...
                </p>
                <button onClick={handleLogout} className="btn btn-secondary w-full">
                  Logout
                </button>
              </div>
            ) : (
              <form onSubmit={handleSubmit}>
                <div className="form-group">
                  <label className="form-label">Username</label>
                  <input
                    type="text"
                    name="username"
                    className="form-input"
                    placeholder="Enter username"
                    value={formData.username}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="form-group">
                  <label className="form-label">Password</label>
                  <input
                    type="password"
                    name="password"
                    className="form-input"
                    placeholder="Enter password"
                    value={formData.password}
                    onChange={handleChange}
                    required
                  />
                </div>

                <button
                  type="submit"
                  className="btn btn-primary w-full"
                  disabled={loading}
                >
                  {loading ? 'Signing In...' : 'Sign In'}
                </button>
              </form>
            )}

            <div className="alert alert-info mt-4">
              <strong>Demo Credentials:</strong><br />
              Username: testuser<br />
              Password: testpass123<br />
              <small>These connect to the real Django authentication system</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Recommendations Page Component
const RecommendationsPage = () => {
  const [recommendations, setRecommendations] = useState([]);
  const [associationRules, setAssociationRules] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({});

  useEffect(() => {
    fetchRecommendationsData();
  }, []);

  const fetchRecommendationsData = async () => {
    try {
      const [recsResponse, rulesResponse] = await Promise.all([
        apiService.getRecommendations(),
        apiService.getAssociationRules()
      ]);

      setRecommendations(recsResponse.results || []);
      setAssociationRules(rulesResponse.results || []);

      // Calculate stats
      const totalRecs = recsResponse.count || 0;
      const totalRules = rulesResponse.count || 0;
      const avgConfidence = rulesResponse.results?.reduce((sum, rule) => sum + rule.confidence, 0) / (rulesResponse.results?.length || 1);

      setStats({
        totalRecommendations: totalRecs,
        totalRules: totalRules,
        avgConfidence: avgConfidence || 0
      });

    } catch (error) {
      console.error('Failed to fetch recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div>
      <Navbar />
      <div className="container" style={{ padding: '2rem 0', minHeight: '80vh' }}>
        <h1 className="text-3xl font-bold mb-4">🤖 AI-Powered Recommendations</h1>
        <p className="text-gray-600 mb-8">
          Explore our machine learning-driven product recommendations and association rules
        </p>

        {loading ? (
          <div className="loading">
            <div className="spinner"></div>
            <span style={{ marginLeft: '1rem' }}>Loading AI recommendations...</span>
          </div>
        ) : (
          <>
            {/* Stats Cards */}
            <div className="product-grid mb-8">
              <div className="card text-center">
                <h3 className="text-2xl font-bold text-blue-600 mb-2">{stats.totalRecommendations}</h3>
                <p className="text-gray-600">Total Recommendations</p>
              </div>
              <div className="card text-center">
                <h3 className="text-2xl font-bold text-green-600 mb-2">{stats.totalRules}</h3>
                <p className="text-gray-600">Association Rules</p>
              </div>
              <div className="card text-center">
                <h3 className="text-2xl font-bold text-purple-600 mb-2">{(stats.avgConfidence * 100).toFixed(1)}%</h3>
                <p className="text-gray-600">Avg Confidence</p>
              </div>
            </div>

            {/* Product Recommendations */}
            {recommendations.length > 0 && (
              <section className="mb-8">
                <h2 className="text-2xl font-bold mb-4">Product Recommendations</h2>
                <div className="product-grid">
                  {recommendations.slice(0, 6).map((rec, index) => (
                    <div key={index} className="card">
                      <div className="mb-4">
                        <h3 className="font-semibold text-lg mb-2">
                          {rec.source_product?.name}
                        </h3>
                        <div style={{ textAlign: 'center', margin: '1rem 0' }}>
                          <span style={{ fontSize: '1.5rem' }}>↓</span>
                        </div>
                        <h4 className="font-medium text-blue-600">
                          {rec.recommended_product?.name}
                        </h4>
                      </div>

                      <div className="text-sm text-gray-600 mb-3">
                        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '0.5rem' }}>
                          <div>Score: {rec.score?.toFixed(2)}</div>
                          <div>Type: {rec.recommendation_type}</div>
                        </div>
                      </div>

                      <span className="recommendation-badge">
                        AI Recommended
                      </span>
                    </div>
                  ))}
                </div>
              </section>
            )}

            {/* Association Rules */}
            {associationRules.length > 0 && (
              <section>
                <h2 className="text-2xl font-bold mb-4">Association Rules</h2>
                <p className="text-gray-600 mb-6">
                  These rules show patterns discovered in customer purchase behavior
                </p>
                <div style={{ display: 'grid', gap: '1rem' }}>
                  {associationRules.slice(0, 8).map((rule, index) => (
                    <div key={index} className="card">
                      <div style={{ display: 'grid', gridTemplateColumns: '1fr auto', gap: '1rem', alignItems: 'center' }}>
                        <div>
                          <p className="font-medium mb-2">
                            <span className="text-blue-600">{rule.antecedent_names?.join(', ')}</span>
                            <span className="mx-2">→</span>
                            <span className="text-green-600">{rule.consequent_names?.join(', ')}</span>
                          </p>
                          <div className="text-sm text-gray-600">
                            <span className="mr-4">Confidence: {(rule.confidence * 100).toFixed(1)}%</span>
                            <span className="mr-4">Support: {(rule.support * 100).toFixed(1)}%</span>
                            <span>Lift: {rule.lift?.toFixed(2)}</span>
                          </div>
                        </div>
                        <div className="text-center">
                          <div className={`w-3 h-3 rounded-full ${
                            rule.confidence > 0.7 ? 'bg-green-500' :
                            rule.confidence > 0.5 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}></div>
                          <div className="text-xs mt-1">
                            {rule.confidence > 0.7 ? 'Strong' :
                             rule.confidence > 0.5 ? 'Good' : 'Fair'}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </section>
            )}

            <div className="alert alert-success mt-8">
              <strong>🧠 Machine Learning Insights:</strong> These recommendations are generated using association rule mining
              algorithms trained on real customer purchase data. The system analyzes patterns to suggest products
              that are frequently bought together.
            </div>
          </>
        )}
      </div>
    </div>
  );
};

// Cart Page Component
const CartPage = () => (
  <div>
    <Navbar />
    <div className="container" style={{ padding: '2rem 0', minHeight: '80vh' }}>
      <h1 className="text-3xl font-bold mb-4">Shopping Cart</h1>
      <div className="alert alert-info">
        <strong>🛒 Cart functionality:</strong> Cart features will be connected to the backend API.
        This shows the frontend structure for cart management with real authentication.
      </div>
    </div>
  </div>
);

function App() {
  return (
    <Router>
      <Routes>
        <Route path="/" element={<HomePage />} />
        <Route path="/products" element={<ProductsPage />} />
        <Route path="/recommendations" element={<RecommendationsPage />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/cart" element={<CartPage />} />
      </Routes>
    </Router>
  );
}

export default App;
