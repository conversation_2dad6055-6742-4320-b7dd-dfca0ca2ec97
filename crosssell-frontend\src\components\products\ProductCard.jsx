import React from 'react';
import { Link } from 'react-router-dom';
import { useCart } from '../../contexts/CartContext';
import { useAuth } from '../../contexts/AuthContext';
import { 
  ShoppingCartIcon, 
  HeartIcon, 
  StarIcon,
  ShoppingBagIcon 
} from '@heroicons/react/24/outline';
import { StarIcon as StarIconSolid } from '@heroicons/react/24/solid';

const ProductCard = ({ product, showRecommendationBadge = false }) => {
  const { addToCart, loading } = useCart();
  const { isAuthenticated } = useAuth();

  const handleAddToCart = async (e) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (!isAuthenticated) {
      // Could show login modal or redirect
      alert('Please login to add items to cart');
      return;
    }

    const result = await addToCart(product.id, 1);
    if (result.success) {
      // Could show success toast
      console.log('Added to cart successfully');
    } else {
      // Could show error toast
      console.error('Failed to add to cart:', result.error);
    }
  };

  const renderStars = (rating, count) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < 5; i++) {
      if (i < fullStars) {
        stars.push(
          <StarIconSolid key={i} className="h-4 w-4 text-yellow-400" />
        );
      } else if (i === fullStars && hasHalfStar) {
        stars.push(
          <div key={i} className="relative">
            <StarIcon className="h-4 w-4 text-gray-300" />
            <div className="absolute inset-0 overflow-hidden w-1/2">
              <StarIconSolid className="h-4 w-4 text-yellow-400" />
            </div>
          </div>
        );
      } else {
        stars.push(
          <StarIcon key={i} className="h-4 w-4 text-gray-300" />
        );
      }
    }

    return (
      <div className="flex items-center space-x-1">
        <div className="flex">{stars}</div>
        <span className="text-sm text-gray-500">({count})</span>
      </div>
    );
  };

  return (
    <div className="product-card group relative">
      {/* Recommendation Badge */}
      {showRecommendationBadge && (
        <div className="absolute top-2 left-2 z-10">
          <span className="recommendation-badge">
            Recommended
          </span>
        </div>
      )}

      {/* Wishlist Button */}
      <button className="absolute top-2 right-2 z-10 p-2 bg-white rounded-full shadow-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-gray-50">
        <HeartIcon className="h-5 w-5 text-gray-600 hover:text-red-500" />
      </button>

      <Link to={`/products/${product.slug}`} className="block">
        {/* Product Image */}
        <div className="aspect-w-1 aspect-h-1 bg-gray-200 rounded-t-lg overflow-hidden">
          {product.primary_image ? (
            <img
              src={product.primary_image}
              alt={product.name}
              className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
            />
          ) : (
            <div className="w-full h-48 bg-gray-100 flex items-center justify-center">
              <ShoppingBagIcon className="h-12 w-12 text-gray-400" />
            </div>
          )}
          
          {/* Stock Status */}
          {!product.is_in_stock && (
            <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
              <span className="text-white font-semibold">Out of Stock</span>
            </div>
          )}
        </div>

        {/* Product Info */}
        <div className="p-4">
          {/* Category */}
          <p className="text-sm text-primary-600 font-medium mb-1">
            {product.category_name}
          </p>

          {/* Product Name */}
          <h3 className="text-lg font-semibold text-gray-900 mb-2 line-clamp-2 group-hover:text-primary-600 transition-colors">
            {product.name}
          </h3>

          {/* Rating */}
          {product.average_rating > 0 && (
            <div className="mb-2">
              {renderStars(product.average_rating, product.rating_count)}
            </div>
          )}

          {/* Price */}
          <div className="flex items-center justify-between mb-3">
            <div className="flex items-center space-x-2">
              <span className="text-xl font-bold text-gray-900">
                ${product.price}
              </span>
              {product.discount_price && (
                <span className="text-sm text-gray-500 line-through">
                  ${product.discount_price}
                </span>
              )}
            </div>
            
            {/* Stock Quantity */}
            {product.stock_quantity <= 10 && product.is_in_stock && (
              <span className="text-xs text-orange-600 font-medium">
                Only {product.stock_quantity} left
              </span>
            )}
          </div>

          {/* Featured Badge */}
          {product.is_featured && (
            <div className="mb-3">
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                Featured
              </span>
            </div>
          )}
        </div>
      </Link>

      {/* Add to Cart Button */}
      <div className="p-4 pt-0">
        <button
          onClick={handleAddToCart}
          disabled={!product.is_in_stock || loading}
          className={`w-full flex items-center justify-center px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
            product.is_in_stock && !loading
              ? 'bg-primary-600 hover:bg-primary-700 text-white'
              : 'bg-gray-300 text-gray-500 cursor-not-allowed'
          }`}
        >
          <ShoppingCartIcon className="h-5 w-5 mr-2" />
          {loading ? 'Adding...' : product.is_in_stock ? 'Add to Cart' : 'Out of Stock'}
        </button>
      </div>
    </div>
  );
};

export default ProductCard;
