import React, { useState, useEffect } from 'react';
import { apiService } from '../../services/api';
import ProductCard from '../products/ProductCard';
import { 
  ShoppingBagIcon, 
  ArrowRightIcon,
  InformationCircleIcon 
} from '@heroicons/react/24/outline';

const CrossSellRecommendations = ({ 
  currentProductId,
  currentProductName = "this product" 
}) => {
  const [crossSellProducts, setCrossSellProducts] = useState([]);
  const [loading, setLoading] = useState(false);
  const [associationRules, setAssociationRules] = useState([]);

  useEffect(() => {
    if (currentProductId) {
      fetchCrossSellRecommendations();
    }
  }, [currentProductId]);

  const fetchCrossSellRecommendations = async () => {
    setLoading(true);
    
    try {
      // Try to get specific recommendations for this product
      const [recommendationsResponse, rulesResponse] = await Promise.all([
        apiService.recommendations.getForProducts([currentProductId]),
        apiService.recommendations.getAssociationRules()
      ]);

      const recommendations = recommendationsResponse.data.recommendations || [];
      const rules = rulesResponse.data.results || [];
      
      // Filter rules that include the current product
      const relevantRules = rules.filter(rule => 
        rule.antecedent_names?.includes(currentProductName) ||
        rule.consequent_names?.includes(currentProductName)
      );
      
      setAssociationRules(relevantRules.slice(0, 3)); // Show top 3 rules
      
      // If we have specific recommendations, use them
      if (recommendations.length > 0) {
        setCrossSellProducts(recommendations.slice(0, 4));
      } else {
        // Fallback: get general product recommendations and filter
        const fallbackResponse = await apiService.recommendations.getProductRecommendations();
        const allRecommendations = fallbackResponse.data.results || [];
        
        // Filter out current product and get cross-sell type recommendations
        const crossSellRecs = allRecommendations
          .filter(rec => 
            rec.recommendation_type === 'cross_sell' &&
            rec.source_product?.id !== currentProductId &&
            rec.recommended_product?.id !== currentProductId
          )
          .slice(0, 4);
        
        const products = crossSellRecs.map(rec => rec.recommended_product).filter(Boolean);
        setCrossSellProducts(products);
      }
    } catch (error) {
      console.error('Failed to fetch cross-sell recommendations:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="py-8 border-t border-gray-200">
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          <span className="ml-2 text-gray-600">Finding perfect matches...</span>
        </div>
      </div>
    );
  }

  if (crossSellProducts.length === 0 && associationRules.length === 0) {
    return null;
  }

  return (
    <div className="py-8 border-t border-gray-200">
      {/* Cross-sell Products */}
      {crossSellProducts.length > 0 && (
        <section className="mb-8">
          <div className="flex items-center justify-between mb-6">
            <div>
              <div className="flex items-center space-x-2 mb-2">
                <ShoppingBagIcon className="h-6 w-6 text-primary-600" />
                <h2 className="text-2xl font-bold text-gray-900">
                  Frequently Bought Together
                </h2>
              </div>
              <p className="text-gray-600">
                Customers who bought this item also purchased these products
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {crossSellProducts.map((product, index) => (
              <ProductCard 
                key={product.id || index} 
                product={product} 
                showRecommendationBadge={true}
              />
            ))}
          </div>
        </section>
      )}

      {/* Association Rules Insights */}
      {associationRules.length > 0 && (
        <section className="bg-gradient-to-r from-primary-50 to-primary-100 rounded-lg p-6">
          <div className="flex items-center space-x-2 mb-4">
            <InformationCircleIcon className="h-6 w-6 text-primary-600" />
            <h3 className="text-lg font-semibold text-gray-900">
              Smart Shopping Insights
            </h3>
          </div>
          
          <div className="space-y-3">
            {associationRules.map((rule, index) => (
              <div key={index} className="bg-white rounded-lg p-4 shadow-sm">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <p className="text-sm text-gray-700">
                      <span className="font-medium">{rule.rule_description}</span>
                    </p>
                    <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                      <span>Confidence: {(rule.confidence * 100).toFixed(1)}%</span>
                      <span>Support: {(rule.support * 100).toFixed(1)}%</span>
                      <span>Lift: {rule.lift.toFixed(2)}x</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    <div className="flex items-center space-x-1">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span className="text-xs text-green-600 font-medium">
                        {rule.confidence > 0.7 ? 'Strong' : rule.confidence > 0.5 ? 'Good' : 'Fair'} Match
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          <div className="mt-4 text-center">
            <p className="text-xs text-gray-500">
              Based on analysis of customer purchase patterns and machine learning algorithms
            </p>
          </div>
        </section>
      )}

      {/* Bundle Offer CTA */}
      {crossSellProducts.length > 0 && (
        <div className="mt-8 bg-gray-50 rounded-lg p-6 text-center">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Save More with Bundle Deals
          </h3>
          <p className="text-gray-600 mb-4">
            Add recommended items to your cart and save on shipping
          </p>
          <button className="btn-primary inline-flex items-center">
            Add Bundle to Cart
            <ArrowRightIcon className="ml-2 h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
};

export default CrossSellRecommendations;
