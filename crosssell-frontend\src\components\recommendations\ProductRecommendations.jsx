import React, { useState, useEffect } from 'react';
import { apiService } from '../../services/api';
import ProductCard from '../products/ProductCard';
import { SparklesIcon, ArrowRightIcon } from '@heroicons/react/24/outline';

const ProductRecommendations = ({ 
  productIds = [], 
  title = "Recommended for You",
  subtitle = "Based on your interests and similar customers",
  maxItems = 4,
  showViewAll = false 
}) => {
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (productIds.length > 0) {
      fetchRecommendations();
    }
  }, [productIds]);

  const fetchRecommendations = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const response = await apiService.recommendations.getForProducts(productIds);
      const recommendationData = response.data.recommendations || [];
      
      // Transform the recommendation data to include product details
      const productRecommendations = recommendationData.slice(0, maxItems);
      setRecommendations(productRecommendations);
    } catch (err) {
      console.error('Failed to fetch recommendations:', err);
      setError('Failed to load recommendations');
      
      // Fallback: try to get general product recommendations
      try {
        const fallbackResponse = await apiService.recommendations.getProductRecommendations();
        const fallbackData = fallbackResponse.data.results || [];
        
        // Extract product data from recommendations
        const products = fallbackData
          .slice(0, maxItems)
          .map(rec => rec.recommended_product)
          .filter(Boolean);
        
        setRecommendations(products);
      } catch (fallbackErr) {
        console.error('Fallback recommendations failed:', fallbackErr);
      }
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="py-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
          <span className="ml-2 text-gray-600">Loading recommendations...</span>
        </div>
      </div>
    );
  }

  if (error && recommendations.length === 0) {
    return (
      <div className="py-8 text-center">
        <SparklesIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <p className="text-gray-500">Unable to load recommendations at this time.</p>
      </div>
    );
  }

  if (recommendations.length === 0) {
    return null;
  }

  return (
    <section className="py-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <div className="flex items-center space-x-2 mb-2">
            <SparklesIcon className="h-6 w-6 text-primary-600" />
            <h2 className="text-2xl font-bold text-gray-900">{title}</h2>
          </div>
          <p className="text-gray-600">{subtitle}</p>
        </div>
        
        {showViewAll && (
          <button className="flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors">
            View All
            <ArrowRightIcon className="ml-1 h-4 w-4" />
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
        {recommendations.map((product, index) => (
          <ProductCard 
            key={product.id || index} 
            product={product} 
            showRecommendationBadge={true}
          />
        ))}
      </div>

      {/* AI Attribution */}
      <div className="mt-6 text-center">
        <p className="text-sm text-gray-500 flex items-center justify-center">
          <SparklesIcon className="h-4 w-4 mr-1" />
          Powered by AI-driven recommendation engine
        </p>
      </div>
    </section>
  );
};

export default ProductRecommendations;
