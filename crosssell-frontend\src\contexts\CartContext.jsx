import React, { createContext, useContext, useState, useEffect } from 'react';
import { apiService } from '../services/api';
import { useAuth } from './AuthContext';

const CartContext = createContext();

export const useCart = () => {
  const context = useContext(CartContext);
  if (!context) {
    throw new Error('useCart must be used within a CartProvider');
  }
  return context;
};

export const CartProvider = ({ children }) => {
  const [cart, setCart] = useState(null);
  const [loading, setLoading] = useState(false);
  const [itemCount, setItemCount] = useState(0);
  const [totalAmount, setTotalAmount] = useState(0);
  const { isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated) {
      fetchCart();
    }
  }, [isAuthenticated]);

  const fetchCart = async () => {
    if (!isAuthenticated) return;
    
    setLoading(true);
    try {
      const response = await apiService.cart.getCurrent();
      setCart(response.data);
      setItemCount(response.data.total_items || 0);
      setTotalAmount(response.data.total_amount || 0);
    } catch (error) {
      console.error('Failed to fetch cart:', error);
      setCart(null);
      setItemCount(0);
      setTotalAmount(0);
    } finally {
      setLoading(false);
    }
  };

  const addToCart = async (productId, quantity = 1) => {
    if (!isAuthenticated) {
      // Redirect to login or show login modal
      return { success: false, error: 'Please login to add items to cart' };
    }

    try {
      await apiService.cart.addItem(productId, quantity);
      await fetchCart(); // Refresh cart
      return { success: true };
    } catch (error) {
      console.error('Failed to add item to cart:', error);
      return { 
        success: false, 
        error: error.response?.data?.detail || 'Failed to add item to cart' 
      };
    }
  };

  const updateCartItem = async (itemId, quantity) => {
    try {
      await apiService.cart.updateItem(itemId, quantity);
      await fetchCart(); // Refresh cart
      return { success: true };
    } catch (error) {
      console.error('Failed to update cart item:', error);
      return { 
        success: false, 
        error: error.response?.data?.detail || 'Failed to update cart item' 
      };
    }
  };

  const removeFromCart = async (itemId) => {
    try {
      await apiService.cart.removeItem(itemId);
      await fetchCart(); // Refresh cart
      return { success: true };
    } catch (error) {
      console.error('Failed to remove item from cart:', error);
      return { 
        success: false, 
        error: error.response?.data?.detail || 'Failed to remove item from cart' 
      };
    }
  };

  const clearCart = async () => {
    try {
      await apiService.cart.clear();
      await fetchCart(); // Refresh cart
      return { success: true };
    } catch (error) {
      console.error('Failed to clear cart:', error);
      return { 
        success: false, 
        error: error.response?.data?.detail || 'Failed to clear cart' 
      };
    }
  };

  const value = {
    cart,
    loading,
    itemCount,
    totalAmount,
    fetchCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
  };

  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  );
};
