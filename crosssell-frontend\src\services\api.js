import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://127.0.0.1:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Token ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Clear token and redirect to login
      localStorage.removeItem('authToken');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// API endpoints
export const apiService = {
  // Authentication
  auth: {
    login: (credentials) => api.post('/api/auth/token/', credentials),
    register: (userData) => api.post('/api/auth/register/', userData),
    getProfile: () => api.get('/api/customers/profile/'),
    updateProfile: (data) => api.patch('/api/customers/profile/', data),
  },

  // Products
  products: {
    getAll: (params = {}) => api.get('/api/products/', { params }),
    getBySlug: (slug) => api.get(`/api/products/${slug}/`),
    getFeatured: () => api.get('/api/products/featured/'),
    getTrending: () => api.get('/api/products/trending/'),
    search: (query) => api.get('/api/products/search/', { params: { q: query } }),
  },

  // Categories
  categories: {
    getAll: () => api.get('/api/categories/'),
  },

  // Cart
  cart: {
    getCurrent: () => api.get('/api/cart/current/'),
    addItem: (productId, quantity = 1) => 
      api.post('/api/cart/add/', { product_id: productId, quantity }),
    updateItem: (itemId, quantity) => 
      api.patch(`/api/cart/items/${itemId}/`, { quantity }),
    removeItem: (itemId) => api.delete(`/api/cart/items/${itemId}/`),
    clear: () => api.delete('/api/cart/clear/'),
  },

  // Orders
  orders: {
    getAll: () => api.get('/api/orders/'),
    getById: (id) => api.get(`/api/orders/${id}/`),
    create: (orderData) => api.post('/api/orders/', orderData),
  },

  // Recommendations
  recommendations: {
    getForProducts: (productIds) => 
      api.get('/recommendations/api/engine/product_recommendations/', {
        params: { product_ids: productIds.join(',') }
      }),
    getAssociationRules: () => api.get('/recommendations/api/association-rules/'),
    getProductRecommendations: () => api.get('/recommendations/api/product-recommendations/'),
  },

  // Reviews
  reviews: {
    getForProduct: (productId) => api.get(`/api/products/${productId}/reviews/`),
    create: (reviewData) => api.post('/api/reviews/', reviewData),
  },
};

export default api;
