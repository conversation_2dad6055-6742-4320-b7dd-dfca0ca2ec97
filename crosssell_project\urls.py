"""
URL configuration for crosssell_project project.
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
# from rest_framework.documentation import include_docs_urls
from rest_framework.authtoken.views import obtain_auth_token

urlpatterns = [
    # Admin
    path('admin/', admin.site.urls),

    # API Authentication
    path('api/auth/token/', obtain_auth_token, name='api_token_auth'),

    # API Documentation (disabled for now)
    # path('api/docs/', include_docs_urls(title='Cross-Sell API')),

    # App APIs
    path('', include('products.urls')),
    path('recommendations/', include('recommendations.urls')),

    # DRF Browsable API
    path('api-auth/', include('rest_framework.urls')),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
