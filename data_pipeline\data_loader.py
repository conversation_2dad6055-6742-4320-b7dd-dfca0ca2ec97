"""
Data loader for Instacart Market Basket Dataset
"""
import os
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple
from pathlib import Path
import logging
from django.conf import settings

logger = logging.getLogger(__name__)


class InstacartDataLoader:
    """
    Load and preprocess Instacart Market Basket Analysis dataset
    
    Dataset structure:
    - aisles.csv: aisle_id, aisle
    - departments.csv: department_id, department  
    - products.csv: product_id, product_name, aisle_id, department_id
    - orders.csv: order_id, user_id, eval_set, order_number, order_dow, order_hour_of_day, days_since_prior_order
    - order_products__prior.csv: order_id, product_id, add_to_cart_order, reordered
    - order_products__train.csv: order_id, product_id, add_to_cart_order, reordered
    """
    
    def __init__(self, data_dir: str = None):
        """Initialize data loader with data directory path"""
        self.data_dir = Path(data_dir) if data_dir else Path(settings.BASE_DIR) / 'data' / 'raw'
        self.processed_dir = Path(settings.BASE_DIR) / 'data' / 'processed'
        
        # Ensure directories exist
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.processed_dir.mkdir(parents=True, exist_ok=True)
        
        # Dataset file paths
        self.file_paths = {
            'aisles': self.data_dir / 'aisles.csv',
            'departments': self.data_dir / 'departments.csv',
            'products': self.data_dir / 'products.csv',
            'orders': self.data_dir / 'orders.csv',
            'order_products_prior': self.data_dir / 'order_products__prior.csv',
            'order_products_train': self.data_dir / 'order_products__train.csv',
        }
        
        # Loaded dataframes
        self.dataframes = {}
        
    def check_data_availability(self) -> Dict[str, bool]:
        """Check which dataset files are available"""
        availability = {}
        for name, path in self.file_paths.items():
            availability[name] = path.exists()
            
        logger.info(f"Data availability: {availability}")
        return availability
    
    def download_sample_data(self):
        """Create sample data for development/testing purposes"""
        logger.info("Creating sample Instacart-like data for development...")
        
        # Sample aisles
        aisles_data = {
            'aisle_id': range(1, 21),
            'aisle': [
                'fresh vegetables', 'fresh fruits', 'packaged vegetables fruits',
                'yogurt', 'packaged cheese', 'water seltzer sparkling water',
                'milk', 'chips pretzels', 'soy lactosefree', 'bread',
                'refrigerated', 'canned goods', 'frozen', 'personal care',
                'meat seafood', 'pantry', 'breakfast', 'canned meals beans',
                'baby food formula', 'cleaning products'
            ]
        }
        
        # Sample departments  
        departments_data = {
            'department_id': range(1, 11),
            'department': [
                'produce', 'dairy eggs', 'beverages', 'snacks', 'pantry',
                'frozen', 'personal care', 'meat seafood', 'babies', 'household'
            ]
        }
        
        # Sample products - use simple integer IDs for sample data
        np.random.seed(42)
        n_products = 1000
        products_data = {
            'product_id': range(1, n_products + 1),
            'product_name': [f'Product_{i}' for i in range(1, n_products + 1)],
            'aisle_id': np.random.randint(1, 21, n_products),
            'department_id': np.random.randint(1, 11, n_products)
        }
        
        # Sample orders
        n_orders = 10000
        n_users = 1000
        orders_data = {
            'order_id': range(1, n_orders + 1),
            'user_id': np.random.randint(1, n_users + 1, n_orders),
            'eval_set': np.random.choice(['prior', 'train'], n_orders, p=[0.8, 0.2]),
            'order_number': np.random.randint(1, 50, n_orders),
            'order_dow': np.random.randint(0, 7, n_orders),
            'order_hour_of_day': np.random.randint(0, 24, n_orders),
            'days_since_prior_order': np.random.choice([None] + list(range(1, 31)), n_orders)
        }
        
        # Create more realistic order products with co-occurrence patterns
        order_products_list = []

        # Define some product groups that are frequently bought together
        product_groups = [
            list(range(1, 51)),      # Group 1: Products 1-50
            list(range(51, 101)),    # Group 2: Products 51-100
            list(range(101, 151)),   # Group 3: Products 101-150
            list(range(151, 201)),   # Group 4: Products 151-200
            list(range(201, 251)),   # Group 5: Products 201-250
        ]

        for order_id in range(1, n_orders + 1):
            # Determine basket size (2-8 items)
            basket_size = np.random.choice([2, 3, 4, 5, 6, 7, 8], p=[0.1, 0.2, 0.3, 0.2, 0.1, 0.05, 0.05])

            # 70% chance to pick from same group, 30% random
            if np.random.random() < 0.7:
                # Pick a product group
                group_idx = np.random.randint(0, len(product_groups))
                group = product_groups[group_idx]
                # Select products from this group
                selected_products = np.random.choice(group, size=min(basket_size, len(group)), replace=False)
            else:
                # Random selection
                selected_products = np.random.choice(range(1, n_products + 1), size=basket_size, replace=False)

            # Add products to order
            for i, product_id in enumerate(selected_products):
                order_products_list.append({
                    'order_id': order_id,
                    'product_id': int(product_id),
                    'add_to_cart_order': i + 1,
                    'reordered': np.random.choice([0, 1], p=[0.6, 0.4])
                })

        order_products_data = pd.DataFrame(order_products_list)
        
        # Create DataFrames and save
        pd.DataFrame(aisles_data).to_csv(self.file_paths['aisles'], index=False)
        pd.DataFrame(departments_data).to_csv(self.file_paths['departments'], index=False)
        pd.DataFrame(products_data).to_csv(self.file_paths['products'], index=False)
        pd.DataFrame(orders_data).to_csv(self.file_paths['orders'], index=False)

        # order_products_data is already a DataFrame
        order_products_df = order_products_data
        orders_df = pd.DataFrame(orders_data)
        
        # Join to get eval_set
        order_products_with_eval = order_products_df.merge(
            orders_df[['order_id', 'eval_set']], 
            on='order_id'
        )
        
        # Save prior and train sets
        prior_data = order_products_with_eval[order_products_with_eval['eval_set'] == 'prior']
        train_data = order_products_with_eval[order_products_with_eval['eval_set'] == 'train']
        
        prior_data.drop('eval_set', axis=1).to_csv(self.file_paths['order_products_prior'], index=False)
        train_data.drop('eval_set', axis=1).to_csv(self.file_paths['order_products_train'], index=False)
        
        logger.info("Sample data created successfully!")
    
    def load_data(self, use_sample: bool = False) -> Dict[str, pd.DataFrame]:
        """Load all dataset files into memory"""
        
        # Check data availability
        availability = self.check_data_availability()
        
        # If no data available or use_sample is True, create sample data
        if not all(availability.values()) or use_sample:
            logger.warning("Some data files missing or use_sample=True. Creating sample data...")
            self.download_sample_data()
        
        # Load all dataframes
        try:
            logger.info("Loading dataset files...")
            
            self.dataframes['aisles'] = pd.read_csv(self.file_paths['aisles'])
            self.dataframes['departments'] = pd.read_csv(self.file_paths['departments'])
            self.dataframes['products'] = pd.read_csv(self.file_paths['products'])
            self.dataframes['orders'] = pd.read_csv(self.file_paths['orders'])
            self.dataframes['order_products_prior'] = pd.read_csv(self.file_paths['order_products_prior'])
            self.dataframes['order_products_train'] = pd.read_csv(self.file_paths['order_products_train'])
            
            logger.info("All dataset files loaded successfully!")
            self._log_data_info()
            
            return self.dataframes
            
        except Exception as e:
            logger.error(f"Error loading data: {e}")
            raise
    
    def _log_data_info(self):
        """Log information about loaded datasets"""
        for name, df in self.dataframes.items():
            logger.info(f"{name}: {df.shape[0]} rows, {df.shape[1]} columns")
    
    def get_data_summary(self) -> Dict:
        """Get summary statistics of the loaded data"""
        if not self.dataframes:
            raise ValueError("No data loaded. Call load_data() first.")
        
        summary = {}
        
        # Basic counts
        summary['total_products'] = len(self.dataframes['products'])
        summary['total_orders'] = len(self.dataframes['orders'])
        summary['total_users'] = self.dataframes['orders']['user_id'].nunique()
        summary['total_aisles'] = len(self.dataframes['aisles'])
        summary['total_departments'] = len(self.dataframes['departments'])
        
        # Order products stats
        prior_orders = len(self.dataframes['order_products_prior'])
        train_orders = len(self.dataframes['order_products_train'])
        summary['total_order_products'] = prior_orders + train_orders
        summary['prior_order_products'] = prior_orders
        summary['train_order_products'] = train_orders
        
        # Average products per order
        all_order_products = pd.concat([
            self.dataframes['order_products_prior'],
            self.dataframes['order_products_train']
        ])
        summary['avg_products_per_order'] = all_order_products.groupby('order_id').size().mean()
        
        # Reorder rate
        summary['reorder_rate'] = all_order_products['reordered'].mean()
        
        return summary
