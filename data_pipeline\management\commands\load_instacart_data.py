"""
Django management command to load and process Instacart data
"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import logging
import time
from pathlib import Path

from data_pipeline.data_loader import InstacartDataLoader
from data_pipeline.preprocessor import MarketBasketPreprocessor

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Load and process Instacart Market Basket dataset'

    def add_arguments(self, parser):
        parser.add_argument(
            '--use-sample',
            action='store_true',
            help='Use sample data instead of real Instacart dataset',
        )
        parser.add_argument(
            '--data-dir',
            type=str,
            help='Directory containing Instacart CSV files',
        )
        parser.add_argument(
            '--min-basket-size',
            type=int,
            default=2,
            help='Minimum number of products per basket (default: 2)',
        )
        parser.add_argument(
            '--max-basket-size',
            type=int,
            default=50,
            help='Maximum number of products per basket (default: 50)',
        )
        parser.add_argument(
            '--sample-baskets',
            type=int,
            help='Number of baskets to sample for processing',
        )
        parser.add_argument(
            '--min-product-frequency',
            type=int,
            default=10,
            help='Minimum frequency for products to be included (default: 10)',
        )
        parser.add_argument(
            '--max-product-frequency-pct',
            type=float,
            default=0.8,
            help='Maximum frequency percentage for products (default: 0.8)',
        )

    def handle(self, *args, **options):
        start_time = time.time()
        
        self.stdout.write(
            self.style.SUCCESS('Starting Instacart data loading and processing...')
        )
        
        try:
            # Initialize data loader
            data_loader = InstacartDataLoader(data_dir=options.get('data_dir'))
            
            # Load data
            self.stdout.write('Loading dataset...')
            dataframes = data_loader.load_data(use_sample=options['use_sample'])
            
            # Get data summary
            summary = data_loader.get_data_summary()
            self.stdout.write(
                self.style.SUCCESS(f"Data loaded successfully!")
            )
            self._print_summary(summary)
            
            # Initialize preprocessor
            self.stdout.write('Preprocessing data for market basket analysis...')
            preprocessor = MarketBasketPreprocessor(dataframes)
            
            # Create market baskets
            baskets = preprocessor.create_market_baskets(
                min_products_per_basket=options['min_basket_size'],
                max_products_per_basket=options['max_basket_size'],
                sample_size=options.get('sample_baskets')
            )
            
            # Analyze basket patterns
            basket_analysis = preprocessor.analyze_basket_patterns(baskets)
            self._print_basket_analysis(basket_analysis)
            
            # Create binary matrix
            self.stdout.write('Creating binary transaction matrix...')
            binary_matrix = preprocessor.create_binary_matrix(baskets)
            
            # Filter products by frequency
            self.stdout.write('Filtering products by frequency...')
            filtered_matrix = preprocessor.filter_products_by_frequency(
                binary_matrix,
                min_frequency=options['min_product_frequency'],
                max_frequency_pct=options['max_product_frequency_pct']
            )
            
            # Save processed data
            self.stdout.write('Saving processed data...')
            preprocessor.save_processed_data()
            
            # Print processing summary
            processing_summary = preprocessor.get_processing_summary()
            self._print_processing_summary(processing_summary)
            
            elapsed_time = time.time() - start_time
            self.stdout.write(
                self.style.SUCCESS(
                    f'Data processing completed successfully in {elapsed_time:.2f} seconds!'
                )
            )
            
        except Exception as e:
            logger.error(f"Error during data processing: {e}")
            raise CommandError(f'Data processing failed: {e}')

    def _print_summary(self, summary):
        """Print data summary"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.WARNING('DATA SUMMARY'))
        self.stdout.write('='*50)
        
        for key, value in summary.items():
            if isinstance(value, float):
                self.stdout.write(f'{key}: {value:.2f}')
            else:
                self.stdout.write(f'{key}: {value:,}')
    
    def _print_basket_analysis(self, analysis):
        """Print basket analysis"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.WARNING('BASKET ANALYSIS'))
        self.stdout.write('='*50)
        
        # Basket size stats
        size_stats = analysis['basket_size_stats']
        self.stdout.write('\nBasket Size Statistics:')
        for key, value in size_stats.items():
            self.stdout.write(f'  {key}: {value:.2f}')
        
        # Top products
        self.stdout.write('\nTop 10 Most Frequent Products:')
        top_products = list(analysis['top_products'].items())[:10]
        for product_id, count in top_products:
            self.stdout.write(f'  Product {product_id}: {count:,} times')
        
        # Product frequency stats
        freq_stats = analysis['product_frequency_stats']
        self.stdout.write('\nProduct Frequency Statistics:')
        for key, value in freq_stats.items():
            if isinstance(value, float):
                self.stdout.write(f'  {key}: {value:.2f}')
            else:
                self.stdout.write(f'  {key}: {value:,}')
    
    def _print_processing_summary(self, summary):
        """Print processing summary"""
        self.stdout.write('\n' + '='*50)
        self.stdout.write(self.style.WARNING('PROCESSING SUMMARY'))
        self.stdout.write('='*50)
        
        for name, info in summary.items():
            self.stdout.write(f'\n{name}:')
            self.stdout.write(f'  Shape: {info["shape"]}')
            self.stdout.write(f'  Memory: {info["memory_usage_mb"]:.2f} MB')
