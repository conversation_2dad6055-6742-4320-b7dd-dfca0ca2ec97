"""
Data preprocessor for market basket analysis
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Set
import logging
from pathlib import Path
from django.conf import settings

logger = logging.getLogger(__name__)


class MarketBasketPreprocessor:
    """
    Preprocess Instacart data for market basket analysis and association rule mining
    """
    
    def __init__(self, dataframes: Dict[str, pd.DataFrame]):
        """Initialize with loaded dataframes"""
        self.dataframes = dataframes
        self.processed_data = {}
        
    def create_market_baskets(self, 
                            min_products_per_basket: int = 2,
                            max_products_per_basket: int = 50,
                            sample_size: Optional[int] = None) -> pd.DataFrame:
        """
        Create market baskets (transactions) from order data
        
        Args:
            min_products_per_basket: Minimum number of products per basket
            max_products_per_basket: Maximum number of products per basket  
            sample_size: Number of baskets to sample (None for all)
            
        Returns:
            DataFrame with columns: basket_id, product_ids (list)
        """
        logger.info("Creating market baskets...")
        
        # Combine prior and train order products
        order_products = pd.concat([
            self.dataframes['order_products_prior'],
            self.dataframes['order_products_train']
        ], ignore_index=True)
        
        # Group products by order_id to create baskets
        baskets = order_products.groupby('order_id')['product_id'].apply(list).reset_index()
        baskets.columns = ['basket_id', 'product_ids']
        
        # Filter baskets by size
        basket_sizes = baskets['product_ids'].apply(len)
        valid_baskets = baskets[
            (basket_sizes >= min_products_per_basket) & 
            (basket_sizes <= max_products_per_basket)
        ].copy()
        
        logger.info(f"Created {len(valid_baskets)} valid baskets from {len(baskets)} total baskets")
        logger.info(f"Filtered out {len(baskets) - len(valid_baskets)} baskets outside size range [{min_products_per_basket}, {max_products_per_basket}]")
        
        # Sample if requested
        if sample_size and sample_size < len(valid_baskets):
            valid_baskets = valid_baskets.sample(n=sample_size, random_state=42).reset_index(drop=True)
            logger.info(f"Sampled {sample_size} baskets")

        # Always reset basket_id to ensure uniqueness
        valid_baskets = valid_baskets.reset_index(drop=True)
        valid_baskets['basket_id'] = range(len(valid_baskets))

        # Add basket statistics
        valid_baskets['basket_size'] = valid_baskets['product_ids'].apply(len)
        
        self.processed_data['market_baskets'] = valid_baskets
        return valid_baskets
    
    def create_binary_matrix(self, baskets: pd.DataFrame = None) -> pd.DataFrame:
        """
        Create binary transaction matrix for association rule mining

        Args:
            baskets: Market baskets DataFrame (uses processed if None)

        Returns:
            Binary matrix where rows are transactions and columns are products
        """
        if baskets is None:
            if 'market_baskets' not in self.processed_data:
                raise ValueError("No market baskets found. Call create_market_baskets() first.")
            baskets = self.processed_data['market_baskets']

        logger.info("Creating binary transaction matrix...")

        # Get all unique products
        all_products = set()
        for product_list in baskets['product_ids']:
            all_products.update(product_list)

        all_products = sorted(list(all_products))
        logger.info(f"Found {len(all_products)} unique products")

        # Ensure unique basket IDs
        baskets = baskets.copy()
        if baskets['basket_id'].duplicated().any():
            logger.warning("Found duplicate basket IDs, creating unique IDs")
            baskets['basket_id'] = range(len(baskets))

        # Create binary matrix using a more robust approach
        n_baskets = len(baskets)
        n_products = len(all_products)

        # Create product to column index mapping
        product_to_idx = {product: idx for idx, product in enumerate(all_products)}

        # Initialize matrix as numpy array for efficiency
        import numpy as np
        matrix_data = np.zeros((n_baskets, n_products), dtype=int)

        # Fill matrix
        for basket_idx, (_, row) in enumerate(baskets.iterrows()):
            products = row['product_ids']
            for product in products:
                if product in product_to_idx:
                    product_idx = product_to_idx[product]
                    matrix_data[basket_idx, product_idx] = 1

        # Convert to DataFrame
        binary_matrix = pd.DataFrame(
            matrix_data,
            index=baskets['basket_id'].values,
            columns=all_products
        )

        logger.info(f"Created binary matrix: {binary_matrix.shape[0]} transactions × {binary_matrix.shape[1]} products")

        self.processed_data['binary_matrix'] = binary_matrix
        return binary_matrix
    
    def filter_products_by_frequency(self, 
                                   binary_matrix: pd.DataFrame = None,
                                   min_frequency: int = 10,
                                   max_frequency_pct: float = 0.8) -> pd.DataFrame:
        """
        Filter products by frequency to remove very rare and very common items
        
        Args:
            binary_matrix: Binary transaction matrix
            min_frequency: Minimum number of transactions a product must appear in
            max_frequency_pct: Maximum percentage of transactions a product can appear in
            
        Returns:
            Filtered binary matrix
        """
        if binary_matrix is None:
            if 'binary_matrix' not in self.processed_data:
                raise ValueError("No binary matrix found. Call create_binary_matrix() first.")
            binary_matrix = self.processed_data['binary_matrix']
        
        logger.info("Filtering products by frequency...")
        
        # Calculate product frequencies
        product_frequencies = binary_matrix.sum(axis=0)
        total_transactions = len(binary_matrix)
        
        # Apply filters
        min_threshold = min_frequency
        max_threshold = int(total_transactions * max_frequency_pct)
        
        valid_products = product_frequencies[
            (product_frequencies >= min_threshold) & 
            (product_frequencies <= max_threshold)
        ].index
        
        filtered_matrix = binary_matrix[valid_products].copy()
        
        logger.info(f"Filtered products: {len(valid_products)} out of {len(product_frequencies)} products kept")
        logger.info(f"Removed {len(product_frequencies) - len(valid_products)} products (too rare or too common)")
        
        self.processed_data['filtered_binary_matrix'] = filtered_matrix
        return filtered_matrix
    
    def get_product_info(self, product_ids: List[int]) -> pd.DataFrame:
        """Get product information for given product IDs"""
        products_df = self.dataframes['products']
        aisles_df = self.dataframes['aisles'] 
        departments_df = self.dataframes['departments']
        
        # Join product info
        product_info = products_df[products_df['product_id'].isin(product_ids)].copy()
        product_info = product_info.merge(aisles_df, on='aisle_id', how='left')
        product_info = product_info.merge(departments_df, on='department_id', how='left')
        
        return product_info
    
    def analyze_basket_patterns(self, baskets: pd.DataFrame = None) -> Dict:
        """Analyze patterns in market baskets"""
        if baskets is None:
            if 'market_baskets' not in self.processed_data:
                raise ValueError("No market baskets found. Call create_market_baskets() first.")
            baskets = self.processed_data['market_baskets']
        
        analysis = {}
        
        # Basket size distribution
        basket_sizes = baskets['basket_size']
        analysis['basket_size_stats'] = {
            'mean': basket_sizes.mean(),
            'median': basket_sizes.median(),
            'std': basket_sizes.std(),
            'min': basket_sizes.min(),
            'max': basket_sizes.max()
        }
        
        # Most frequent products
        all_products = []
        for product_list in baskets['product_ids']:
            all_products.extend(product_list)
        
        product_counts = pd.Series(all_products).value_counts()
        analysis['top_products'] = product_counts.head(20).to_dict()
        
        # Product frequency distribution
        analysis['product_frequency_stats'] = {
            'total_unique_products': len(product_counts),
            'mean_frequency': product_counts.mean(),
            'median_frequency': product_counts.median()
        }
        
        return analysis
    
    def save_processed_data(self, output_dir: str = None):
        """Save processed data to files"""
        if output_dir is None:
            output_dir = Path(settings.BASE_DIR) / 'data' / 'processed'
        else:
            output_dir = Path(output_dir)
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for name, data in self.processed_data.items():
            file_path = output_dir / f"{name}.csv"
            
            if isinstance(data, pd.DataFrame):
                if name == 'market_baskets':
                    # Special handling for list column
                    data_to_save = data.copy()
                    data_to_save['product_ids'] = data_to_save['product_ids'].apply(
                        lambda x: ','.join(map(str, x))
                    )
                    data_to_save.to_csv(file_path, index=False)
                else:
                    data.to_csv(file_path, index=True)
                    
                logger.info(f"Saved {name} to {file_path}")
    
    def load_processed_data(self, input_dir: str = None):
        """Load previously processed data"""
        if input_dir is None:
            input_dir = Path(settings.BASE_DIR) / 'data' / 'processed'
        else:
            input_dir = Path(input_dir)
        
        # Load market baskets
        baskets_path = input_dir / 'market_baskets.csv'
        if baskets_path.exists():
            baskets = pd.read_csv(baskets_path)
            baskets['product_ids'] = baskets['product_ids'].apply(
                lambda x: [int(i) for i in x.split(',')]
            )
            self.processed_data['market_baskets'] = baskets
            logger.info(f"Loaded market baskets from {baskets_path}")
        
        # Load binary matrices
        for matrix_name in ['binary_matrix', 'filtered_binary_matrix']:
            matrix_path = input_dir / f'{matrix_name}.csv'
            if matrix_path.exists():
                matrix = pd.read_csv(matrix_path, index_col=0)
                self.processed_data[matrix_name] = matrix
                logger.info(f"Loaded {matrix_name} from {matrix_path}")
    
    def get_processing_summary(self) -> Dict:
        """Get summary of processed data"""
        summary = {}
        
        for name, data in self.processed_data.items():
            if isinstance(data, pd.DataFrame):
                summary[name] = {
                    'shape': data.shape,
                    'memory_usage_mb': data.memory_usage(deep=True).sum() / 1024 / 1024
                }
        
        return summary
