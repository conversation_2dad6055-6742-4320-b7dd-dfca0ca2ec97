"""
Celery tasks for data pipeline processing
"""
from celery import shared_task
from django.conf import settings
import logging
import time
from pathlib import Path

from .data_loader import InstacartDataLoader
from .preprocessor import MarketBasketPreprocessor

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def process_instacart_data(self, 
                          use_sample=True,
                          data_dir=None,
                          min_basket_size=2,
                          max_basket_size=50,
                          sample_baskets=None,
                          min_product_frequency=10,
                          max_product_frequency_pct=0.8):
    """
    Celery task to process Instacart data in the background
    
    Args:
        use_sample: Whether to use sample data
        data_dir: Directory containing data files
        min_basket_size: Minimum products per basket
        max_basket_size: Maximum products per basket
        sample_baskets: Number of baskets to sample
        min_product_frequency: Minimum product frequency
        max_product_frequency_pct: Maximum product frequency percentage
    
    Returns:
        dict: Processing results and statistics
    """
    
    try:
        # Update task state
        self.update_state(state='PROGRESS', meta={'step': 'Loading data', 'progress': 10})
        
        # Initialize data loader
        data_loader = InstacartDataLoader(data_dir=data_dir)
        
        # Load data
        logger.info("Loading Instacart dataset...")
        dataframes = data_loader.load_data(use_sample=use_sample)
        
        # Get data summary
        data_summary = data_loader.get_data_summary()
        logger.info(f"Data loaded: {data_summary}")
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'step': 'Preprocessing data', 'progress': 30})
        
        # Initialize preprocessor
        preprocessor = MarketBasketPreprocessor(dataframes)
        
        # Create market baskets
        logger.info("Creating market baskets...")
        baskets = preprocessor.create_market_baskets(
            min_products_per_basket=min_basket_size,
            max_products_per_basket=max_basket_size,
            sample_size=sample_baskets
        )
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'step': 'Analyzing baskets', 'progress': 50})
        
        # Analyze basket patterns
        basket_analysis = preprocessor.analyze_basket_patterns(baskets)
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'step': 'Creating binary matrix', 'progress': 70})
        
        # Create binary matrix
        logger.info("Creating binary transaction matrix...")
        binary_matrix = preprocessor.create_binary_matrix(baskets)
        
        # Filter products by frequency
        logger.info("Filtering products by frequency...")
        filtered_matrix = preprocessor.filter_products_by_frequency(
            binary_matrix,
            min_frequency=min_product_frequency,
            max_frequency_pct=max_product_frequency_pct
        )
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'step': 'Saving processed data', 'progress': 90})
        
        # Save processed data
        logger.info("Saving processed data...")
        preprocessor.save_processed_data()
        
        # Get processing summary
        processing_summary = preprocessor.get_processing_summary()
        
        # Prepare results
        results = {
            'status': 'completed',
            'data_summary': data_summary,
            'basket_analysis': basket_analysis,
            'processing_summary': processing_summary,
            'parameters': {
                'use_sample': use_sample,
                'min_basket_size': min_basket_size,
                'max_basket_size': max_basket_size,
                'sample_baskets': sample_baskets,
                'min_product_frequency': min_product_frequency,
                'max_product_frequency_pct': max_product_frequency_pct
            }
        }
        
        logger.info("Data processing completed successfully!")
        return results
        
    except Exception as e:
        logger.error(f"Error in data processing task: {e}")
        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'step': 'Error occurred'}
        )
        raise


@shared_task
def validate_data_quality(data_dir=None):
    """
    Task to validate data quality and completeness
    
    Args:
        data_dir: Directory containing data files
        
    Returns:
        dict: Data quality report
    """
    try:
        data_loader = InstacartDataLoader(data_dir=data_dir)
        
        # Check data availability
        availability = data_loader.check_data_availability()
        
        quality_report = {
            'timestamp': time.time(),
            'data_availability': availability,
            'all_files_present': all(availability.values()),
            'missing_files': [name for name, available in availability.items() if not available]
        }
        
        # If data is available, perform additional checks
        if quality_report['all_files_present']:
            try:
                dataframes = data_loader.load_data()
                data_summary = data_loader.get_data_summary()
                
                quality_report.update({
                    'data_loaded_successfully': True,
                    'data_summary': data_summary,
                    'data_quality_issues': []
                })
                
                # Check for data quality issues
                issues = []
                
                # Check for empty dataframes
                for name, df in dataframes.items():
                    if df.empty:
                        issues.append(f"{name} is empty")
                
                # Check for missing values in key columns
                if 'products' in dataframes:
                    products_df = dataframes['products']
                    if products_df['product_name'].isnull().any():
                        issues.append("Missing product names found")
                
                if 'orders' in dataframes:
                    orders_df = dataframes['orders']
                    if orders_df['user_id'].isnull().any():
                        issues.append("Missing user IDs in orders")
                
                quality_report['data_quality_issues'] = issues
                quality_report['has_quality_issues'] = len(issues) > 0
                
            except Exception as e:
                quality_report.update({
                    'data_loaded_successfully': False,
                    'load_error': str(e)
                })
        
        return quality_report
        
    except Exception as e:
        logger.error(f"Error in data validation task: {e}")
        return {
            'timestamp': time.time(),
            'error': str(e),
            'validation_failed': True
        }


@shared_task
def cleanup_old_processed_data(days_old=7):
    """
    Task to cleanup old processed data files
    
    Args:
        days_old: Number of days after which to delete old files
        
    Returns:
        dict: Cleanup results
    """
    try:
        processed_dir = Path(settings.BASE_DIR) / 'data' / 'processed'
        
        if not processed_dir.exists():
            return {'status': 'no_data_to_cleanup'}
        
        import os
        import time
        
        current_time = time.time()
        cutoff_time = current_time - (days_old * 24 * 60 * 60)
        
        deleted_files = []
        total_size_freed = 0
        
        for file_path in processed_dir.glob('*'):
            if file_path.is_file():
                file_mtime = os.path.getmtime(file_path)
                if file_mtime < cutoff_time:
                    file_size = file_path.stat().st_size
                    file_path.unlink()
                    deleted_files.append(str(file_path.name))
                    total_size_freed += file_size
        
        return {
            'status': 'completed',
            'deleted_files': deleted_files,
            'files_deleted_count': len(deleted_files),
            'total_size_freed_mb': total_size_freed / (1024 * 1024),
            'cutoff_days': days_old
        }
        
    except Exception as e:
        logger.error(f"Error in cleanup task: {e}")
        return {
            'status': 'failed',
            'error': str(e)
        }
