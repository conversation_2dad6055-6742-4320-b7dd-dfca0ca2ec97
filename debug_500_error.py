#!/usr/bin/env python
"""
Debug script to identify the exact 500 error
"""
import os
import django
from django.test import RequestFactory
from django.urls import reverse, resolve

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'crosssell_project.settings')
django.setup()

def test_url_resolution():
    """Test URL resolution"""
    print("🔍 Testing URL Resolution...")
    
    try:
        # Test URL patterns
        from django.urls import reverse
        
        # This should work if URLs are configured correctly
        print("   Testing URL reverse lookup...")
        
        # Test direct URL resolution
        from django.urls import resolve
        resolver = resolve('/api/categories/')
        print(f"   ✅ URL resolves to: {resolver.func}")
        print(f"   ✅ View name: {resolver.view_name}")
        print(f"   ✅ URL name: {resolver.url_name}")
        
    except Exception as e:
        print(f"   ❌ URL resolution error: {e}")
        import traceback
        traceback.print_exc()

def test_viewset_directly():
    """Test ViewSet directly"""
    print("\n🔍 Testing ViewSet Directly...")
    
    try:
        from products.views import CategoryViewSet
        from django.test import RequestFactory
        
        # Create a mock request
        factory = RequestFactory()
        request = factory.get('/api/categories/')
        
        # Test ViewSet instantiation
        viewset = CategoryViewSet()
        viewset.request = request
        viewset.format_kwarg = None
        
        print("   ✅ ViewSet instantiated")
        
        # Test get_queryset
        queryset = viewset.get_queryset()
        print(f"   ✅ Queryset: {queryset.count()} items")
        
        # Test get_serializer_class
        serializer_class = viewset.get_serializer_class()
        print(f"   ✅ Serializer class: {serializer_class}")
        
        # Test list action
        viewset.action = 'list'
        response = viewset.list(request)
        print(f"   ✅ List response status: {response.status_code}")
        print(f"   ✅ Response data keys: {list(response.data.keys()) if hasattr(response, 'data') else 'No data'}")
        
    except Exception as e:
        print(f"   ❌ ViewSet error: {e}")
        import traceback
        traceback.print_exc()

def test_serializer_directly():
    """Test serializer directly"""
    print("\n🔍 Testing Serializer Directly...")
    
    try:
        from products.serializers import CategorySerializer
        from products.models import Category
        
        # Get a category
        category = Category.objects.first()
        if category:
            serializer = CategorySerializer(category)
            print(f"   ✅ Serializer data: {serializer.data}")
        else:
            print("   ⚠️ No categories found")
            
    except Exception as e:
        print(f"   ❌ Serializer error: {e}")
        import traceback
        traceback.print_exc()

def test_router_configuration():
    """Test router configuration"""
    print("\n🔍 Testing Router Configuration...")
    
    try:
        from products.urls import router
        
        print(f"   ✅ Router registry: {list(router.registry)}")
        
        # Test URL patterns
        urlpatterns = router.urls
        print(f"   ✅ Generated URL patterns: {len(urlpatterns)} patterns")
        
        for pattern in urlpatterns[:5]:  # Show first 5
            print(f"      - {pattern.pattern}")
            
    except Exception as e:
        print(f"   ❌ Router error: {e}")
        import traceback
        traceback.print_exc()

def test_django_request_cycle():
    """Test the full Django request cycle"""
    print("\n🔍 Testing Full Django Request Cycle...")
    
    try:
        from django.test import Client
        
        client = Client()
        
        # Test with Django test client
        response = client.get('/api/categories/')
        print(f"   Response status: {response.status_code}")
        
        if response.status_code == 500:
            print("   ❌ 500 error in test client too")
            # Try to get more details
            if hasattr(response, 'content'):
                content = response.content.decode('utf-8')
                if 'Exception Value:' in content:
                    lines = content.split('\n')
                    for i, line in enumerate(lines):
                        if 'Exception Value:' in line:
                            print(f"   Exception: {lines[i+1].strip() if i+1 < len(lines) else 'Unknown'}")
                            break
        else:
            print(f"   ✅ Success! Response: {response.json() if response.status_code == 200 else 'Non-JSON'}")
            
    except Exception as e:
        print(f"   ❌ Request cycle error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("🧪 Debugging 500 Errors")
    print("=" * 40)
    
    test_url_resolution()
    test_router_configuration()
    test_serializer_directly()
    test_viewset_directly()
    test_django_request_cycle()
    
    print("\n" + "=" * 40)
    print("✅ Debug completed!")
