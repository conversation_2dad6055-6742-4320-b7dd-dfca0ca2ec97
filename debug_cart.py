#!/usr/bin/env python
"""
Debug Cart API issues
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'crosssell_project.settings')
django.setup()

from django.contrib.auth.models import User
from products.models import Customer, Cart, CartItem
from products.serializers import CartSerializer

def test_cart_model():
    """Test Cart model directly"""
    print("🛒 Testing Cart Model...")
    
    try:
        # Get test user
        user = User.objects.get(username='testuser')
        print(f"✅ Found user: {user.username}")
        
        # Check customer profile
        try:
            customer = user.customer_profile
            print(f"✅ Customer profile exists: {customer}")
        except Customer.DoesNotExist:
            print("⚠️ Creating customer profile...")
            customer = Customer.objects.create(user=user)
            print(f"✅ Created customer profile: {customer}")
        
        # Test cart creation
        cart, created = Cart.objects.get_or_create(customer=customer)
        print(f"✅ Cart {'created' if created else 'found'}: {cart}")
        
        # Test serializer
        serializer = CartSerializer(cart)
        print(f"✅ Serializer data: {serializer.data}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_cart_viewset():
    """Test CartViewSet methods"""
    print("\n🔍 Testing CartViewSet...")
    
    try:
        from products.views import CartViewSet
        from django.test import RequestFactory
        from django.contrib.auth.models import AnonymousUser
        
        # Create mock request
        factory = RequestFactory()
        request = factory.get('/api/cart/current/')
        
        # Test with authenticated user
        user = User.objects.get(username='testuser')
        request.user = user
        
        # Create viewset
        viewset = CartViewSet()
        viewset.request = request
        viewset.format_kwarg = None
        
        print("✅ ViewSet created")
        
        # Test get_or_create_cart
        cart = viewset.get_or_create_cart()
        print(f"✅ get_or_create_cart: {cart}")
        
        # Test queryset
        queryset = viewset.get_queryset()
        print(f"✅ get_queryset: {queryset.count()} carts")
        
    except Exception as e:
        print(f"❌ ViewSet error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("🧪 Cart API Debug")
    print("=" * 30)
    
    test_cart_model()
    test_cart_viewset()
    
    print("\n✅ Debug completed!")
