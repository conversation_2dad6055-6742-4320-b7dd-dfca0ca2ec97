#!/usr/bin/env python
"""
Debug Product Recommendations API issues
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'crosssell_project.settings')
django.setup()

from recommendations.models import ProductRecommendation
from recommendations.serializers import ProductRecommendationSerializer

def test_product_recommendation_model():
    """Test ProductRecommendation model and serializer"""
    print("🎯 Testing ProductRecommendation Model...")
    
    try:
        # Get a sample recommendation
        recommendation = ProductRecommendation.objects.first()
        if recommendation:
            print(f"✅ Found recommendation: {recommendation}")
            
            # Test serializer
            serializer = ProductRecommendationSerializer(recommendation)
            print(f"✅ Serializer data: {serializer.data}")
        else:
            print("⚠️ No recommendations found in database")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def test_product_recommendation_viewset():
    """Test ProductRecommendationViewSet"""
    print("\n🔍 Testing ProductRecommendationViewSet...")
    
    try:
        from recommendations.views import ProductRecommendationViewSet
        from django.test import RequestFactory
        
        # Create mock request
        factory = RequestFactory()
        request = factory.get('/recommendations/api/product-recommendations/')
        
        # Create viewset
        viewset = ProductRecommendationViewSet()
        viewset.request = request
        viewset.format_kwarg = None
        
        print("✅ ViewSet created")
        
        # Test queryset
        queryset = viewset.get_queryset()
        print(f"✅ get_queryset: {queryset.count()} recommendations")
        
        # Test serializer class
        serializer_class = viewset.get_serializer_class()
        print(f"✅ get_serializer_class: {serializer_class}")
        
    except Exception as e:
        print(f"❌ ViewSet error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    print("🧪 Product Recommendations API Debug")
    print("=" * 40)
    
    test_product_recommendation_model()
    test_product_recommendation_viewset()
    
    print("\n✅ Debug completed!")
