#!/usr/bin/env python
"""
Debug script to test individual components
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'crosssell_project.settings')
django.setup()

def test_models():
    """Test if models can be imported and used"""
    print("🔍 Testing Models...")
    try:
        from products.models import Category, Product, Customer
        print("✅ Products models imported successfully")
        
        # Test basic queries
        categories = Category.objects.all()
        products = Product.objects.all()
        print(f"   Categories: {categories.count()}")
        print(f"   Products: {products.count()}")
        
    except Exception as e:
        print(f"❌ Models error: {e}")

def test_serializers():
    """Test if serializers can be imported"""
    print("\n🔍 Testing Serializers...")
    try:
        from products.serializers import (
            CategorySerializer, ProductListSerializer, ProductDetailSerializer,
            CustomerSerializer, CartSerializer, CartItemSerializer, OrderSerializer,
            ProductReviewSerializer, ProductViewSerializer, ProductSearchSerializer,
            BulkCartUpdateSerializer
        )
        print("✅ Products serializers imported successfully")
        
    except Exception as e:
        print(f"❌ Serializers error: {e}")

def test_views():
    """Test if views can be imported"""
    print("\n🔍 Testing Views...")
    try:
        from products.views import CategoryViewSet, ProductViewSet
        print("✅ Products views imported successfully")
        
        # Test ViewSet instantiation
        category_viewset = CategoryViewSet()
        product_viewset = ProductViewSet()
        print("✅ ViewSets instantiated successfully")
        
    except Exception as e:
        print(f"❌ Views error: {e}")

def test_urls():
    """Test URL configuration"""
    print("\n🔍 Testing URLs...")
    try:
        from django.urls import reverse
        from django.test import RequestFactory
        
        # Test URL resolution
        factory = RequestFactory()
        request = factory.get('/api/categories/')
        
        print("✅ URL configuration seems OK")
        
    except Exception as e:
        print(f"❌ URLs error: {e}")

def test_permissions():
    """Test permissions"""
    print("\n🔍 Testing Permissions...")
    try:
        from products.permissions import IsOwnerOrReadOnly, IsCustomerOrReadOnly
        print("✅ Permissions imported successfully")
        
    except Exception as e:
        print(f"❌ Permissions error: {e}")

def test_database_connection():
    """Test database connection"""
    print("\n🔍 Testing Database Connection...")
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
        print("✅ Database connection successful")
        
    except Exception as e:
        print(f"❌ Database error: {e}")

if __name__ == '__main__':
    print("🧪 Django Component Debug Test")
    print("=" * 40)
    
    test_database_connection()
    test_models()
    test_serializers()
    test_views()
    test_urls()
    test_permissions()
    
    print("\n" + "=" * 40)
    print("✅ Debug test completed!")
