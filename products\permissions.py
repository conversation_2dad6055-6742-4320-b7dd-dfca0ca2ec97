"""
Custom permissions for Products API
"""
from rest_framework import permissions


class IsOwnerOrReadOnly(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to edit it.
    """

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Write permissions are only allowed to the owner of the object.
        return obj.customer.user == request.user


class IsCustomerOrReadOnly(permissions.BasePermission):
    """
    Permission for customer-specific resources
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        # Check if user has customer profile
        if hasattr(request.user, 'customer_profile'):
            return obj.customer == request.user.customer_profile
        return False


class IsAdminOrReadOnly(permissions.BasePermission):
    """
    Permission for admin-only write operations
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        return request.user.is_authenticated and request.user.is_staff


class CanManageProducts(permissions.BasePermission):
    """
    Permission for product management
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        return (
            request.user.is_authenticated and 
            (request.user.is_staff or request.user.has_perm('products.change_product'))
        )


class CanViewAnalytics(permissions.BasePermission):
    """
    Permission for viewing analytics data
    """

    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and 
            (request.user.is_staff or request.user.has_perm('products.view_analytics'))
        )
