"""
Serializers for Products API
"""
from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    Category, Product, Customer, Cart, CartItem, 
    Order, OrderItem, ProductReview, ProductView
)


class CategorySerializer(serializers.ModelSerializer):
    """Serializer for Category model"""
    product_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Category
        fields = [
            'id', 'name', 'slug', 'description',
            'is_active', 'parent', 'product_count', 'created_at'
        ]
        read_only_fields = ['id', 'created_at', 'product_count']
    
    def get_product_count(self, obj):
        return obj.products.filter(is_active=True).count()


class ProductListSerializer(serializers.ModelSerializer):
    """Lightweight serializer for product lists"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    average_rating = serializers.SerializerMethodField()
    rating_count = serializers.SerializerMethodField()
    primary_image = serializers.SerializerMethodField()
    is_in_stock = serializers.ReadOnlyField()

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'price', 'discount_price', 'primary_image',
            'category_name', 'is_featured', 'is_in_stock', 'stock_quantity',
            'average_rating', 'rating_count', 'popularity_score'
        ]

    def get_average_rating(self, obj):
        return round(obj.rating_average, 1) if obj.rating_average else 0.0

    def get_rating_count(self, obj):
        return obj.reviews.filter(is_approved=True).count()

    def get_primary_image(self, obj):
        primary_image = obj.images.filter(is_primary=True).first()
        if primary_image:
            return self.context['request'].build_absolute_uri(primary_image.image.url) if primary_image.image else None
        return None


class ProductDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for individual product views"""
    category = CategorySerializer(read_only=True)
    category_id = serializers.PrimaryKeyRelatedField(
        queryset=Category.objects.filter(is_active=True),
        source='category',
        write_only=True
    )
    average_rating = serializers.SerializerMethodField()
    reviews_count = serializers.SerializerMethodField()
    images = serializers.SerializerMethodField()
    is_in_stock = serializers.ReadOnlyField()

    class Meta:
        model = Product
        fields = [
            'id', 'name', 'slug', 'description', 'short_description',
            'price', 'discount_price', 'cost_price', 'sku', 'brand',
            'category', 'category_id', 'images',
            'weight', 'dimensions', 'stock_quantity', 'low_stock_threshold',
            'track_inventory', 'is_active', 'is_featured',
            'is_digital', 'popularity_score',
            'rating_average', 'average_rating', 'reviews_count',
            'is_in_stock', 'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'slug', 'popularity_score', 'rating_average',
            'created_at', 'updated_at', 'average_rating', 'reviews_count', 'is_in_stock'
        ]

    def get_average_rating(self, obj):
        return round(obj.rating_average, 1) if obj.rating_average else 0.0

    def get_reviews_count(self, obj):
        return obj.reviews.filter(is_approved=True).count()

    def get_images(self, obj):
        images = obj.images.all().order_by('order', 'created_at')
        request = self.context.get('request')
        if request:
            return [request.build_absolute_uri(img.image.url) for img in images if img.image]
        return [img.image.url for img in images if img.image]


class CustomerSerializer(serializers.ModelSerializer):
    """Serializer for Customer model"""
    username = serializers.CharField(source='user.username', read_only=True)
    email = serializers.EmailField(source='user.email', read_only=True)
    first_name = serializers.CharField(source='user.first_name', read_only=True)
    last_name = serializers.CharField(source='user.last_name', read_only=True)
    full_name = serializers.SerializerMethodField()
    
    class Meta:
        model = Customer
        fields = [
            'id', 'username', 'email', 'first_name', 'last_name', 'full_name',
            'phone', 'date_of_birth', 'address_line_1', 'address_line_2',
            'city', 'state', 'postal_code', 'country', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']
    
    def get_full_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}".strip()


class CartItemSerializer(serializers.ModelSerializer):
    """Serializer for Cart Items"""
    product = ProductListSerializer(read_only=True)
    product_id = serializers.PrimaryKeyRelatedField(
        queryset=Product.objects.filter(is_active=True),
        source='product',
        write_only=True
    )
    total_price = serializers.ReadOnlyField()
    
    class Meta:
        model = CartItem
        fields = [
            'id', 'product', 'product_id', 'quantity', 'unit_price',
            'total_price', 'added_at', 'updated_at'
        ]
        read_only_fields = ['id', 'unit_price', 'total_price', 'added_at', 'updated_at']
    
    def validate_quantity(self, value):
        if value <= 0:
            raise serializers.ValidationError("Quantity must be greater than 0")
        return value


class CartSerializer(serializers.ModelSerializer):
    """Serializer for Shopping Cart"""
    items = CartItemSerializer(many=True, read_only=True)
    total_amount = serializers.ReadOnlyField()
    total_items = serializers.SerializerMethodField()
    
    class Meta:
        model = Cart
        fields = [
            'id', 'session_key', 'items', 'total_amount', 'total_items',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'total_amount', 'total_items', 'created_at', 'updated_at']
    
    def get_total_items(self, obj):
        return sum(item.quantity for item in obj.items.all())


class OrderItemSerializer(serializers.ModelSerializer):
    """Serializer for Order Items"""
    product = ProductListSerializer(read_only=True)
    
    class Meta:
        model = OrderItem
        fields = [
            'id', 'product', 'quantity', 'unit_price', 'total_price',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class OrderSerializer(serializers.ModelSerializer):
    """Serializer for Orders"""
    customer = CustomerSerializer(read_only=True)
    items = OrderItemSerializer(many=True, read_only=True)
    
    class Meta:
        model = Order
        fields = [
            'id', 'order_number', 'customer', 'status', 'payment_status',
            'payment_method', 'payment_reference', 'subtotal', 'tax_amount',
            'shipping_amount', 'discount_amount', 'total_amount', 'currency',
            'shipping_address', 'billing_address', 'notes', 'items',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'order_number', 'subtotal', 'tax_amount', 'total_amount',
            'created_at', 'updated_at'
        ]


class ProductReviewSerializer(serializers.ModelSerializer):
    """Serializer for Product Reviews"""
    customer = CustomerSerializer(read_only=True)
    customer_name = serializers.SerializerMethodField()
    
    class Meta:
        model = ProductReview
        fields = [
            'id', 'product', 'customer', 'customer_name', 'rating', 'title',
            'comment', 'is_approved', 'is_verified_purchase', 'helpful_count',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'id', 'customer', 'is_approved', 'is_verified_purchase',
            'helpful_count', 'created_at', 'updated_at', 'customer_name'
        ]
    
    def get_customer_name(self, obj):
        return f"{obj.customer.user.first_name} {obj.customer.user.last_name}".strip()
    
    def validate_rating(self, value):
        if not 1 <= value <= 5:
            raise serializers.ValidationError("Rating must be between 1 and 5")
        return value


class ProductViewSerializer(serializers.ModelSerializer):
    """Serializer for Product Views (Analytics)"""
    product = ProductListSerializer(read_only=True)
    customer = CustomerSerializer(read_only=True)
    
    class Meta:
        model = ProductView
        fields = [
            'id', 'product', 'customer', 'session_key', 'ip_address',
            'user_agent', 'referrer', 'viewed_at'
        ]
        read_only_fields = ['id', 'viewed_at']


class ProductSearchSerializer(serializers.Serializer):
    """Serializer for product search parameters"""
    q = serializers.CharField(required=False, help_text="Search query")
    category = serializers.CharField(required=False, help_text="Category slug")
    min_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    max_price = serializers.DecimalField(max_digits=10, decimal_places=2, required=False)
    in_stock = serializers.BooleanField(required=False, default=True)
    featured = serializers.BooleanField(required=False)
    sort_by = serializers.ChoiceField(
        choices=[
            'name', '-name', 'price', '-price', 'created_at', '-created_at',
            'popularity_score', '-popularity_score', 'rating_average', '-rating_average'
        ],
        required=False,
        default='-created_at'
    )


class BulkCartUpdateSerializer(serializers.Serializer):
    """Serializer for bulk cart updates"""
    items = serializers.ListField(
        child=serializers.DictField(
            child=serializers.CharField()
        ),
        help_text="List of items with product_id and quantity"
    )
    
    def validate_items(self, value):
        for item in value:
            if 'product_id' not in item or 'quantity' not in item:
                raise serializers.ValidationError(
                    "Each item must have 'product_id' and 'quantity'"
                )
            try:
                quantity = int(item['quantity'])
                if quantity <= 0:
                    raise serializers.ValidationError("Quantity must be greater than 0")
            except (ValueError, TypeError):
                raise serializers.ValidationError("Quantity must be a valid integer")
        return value
