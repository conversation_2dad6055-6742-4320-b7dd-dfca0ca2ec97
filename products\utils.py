"""
Utility functions for the products app
"""
from django.db.models import Count, Sum, Avg, Q
from django.utils import timezone
from datetime import timedelta
from decimal import Decimal


def get_client_ip(request):
    """Get client IP address from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def calculate_popularity_score(product):
    """
    Calculate popularity score based on various factors:
    - Order count (30%)
    - View count (20%)
    - Rating average (25%)
    - Recent activity (25%)
    """
    now = timezone.now()
    last_30_days = now - timedelta(days=30)
    
    # Get metrics
    order_count = product.orderitem_set.count()
    view_count = product.views.count()
    recent_views = product.views.filter(viewed_at__gte=last_30_days).count()
    recent_orders = product.orderitem_set.filter(created_at__gte=last_30_days).count()
    
    # Normalize scores (0-1 scale)
    max_orders = 100  # Configurable threshold
    max_views = 1000  # Configurable threshold
    
    order_score = min(order_count / max_orders, 1.0) * 0.30
    view_score = min(view_count / max_views, 1.0) * 0.20
    rating_score = (product.rating_average / 5.0) * 0.25
    
    # Recent activity boost
    recent_activity_score = (
        min(recent_views / 100, 1.0) * 0.125 + 
        min(recent_orders / 10, 1.0) * 0.125
    )
    
    return order_score + view_score + rating_score + recent_activity_score


def update_product_ratings(product):
    """Update product rating statistics"""
    reviews = product.reviews.filter(is_approved=True)
    
    if reviews.exists():
        product.rating_average = reviews.aggregate(avg_rating=Avg('rating'))['avg_rating']
        product.rating_count = reviews.count()
    else:
        product.rating_average = 0.0
        product.rating_count = 0
    
    product.save(update_fields=['rating_average', 'rating_count'])


def get_trending_products(limit=10, days=7):
    """Get trending products based on recent activity"""
    from django.db import models
    from .models import Product, ProductView, OrderItem

    cutoff_date = timezone.now() - timedelta(days=days)

    # Get products with recent activity
    trending_products = Product.objects.filter(
        is_active=True
    ).annotate(
        recent_views=Count('views', filter=models.Q(views__viewed_at__gte=cutoff_date)),
        recent_orders=Count('orderitem', filter=models.Q(orderitem__created_at__gte=cutoff_date))
    ).filter(
        models.Q(recent_views__gt=0) | models.Q(recent_orders__gt=0)
    ).order_by('-recent_orders', '-recent_views')[:limit]

    return trending_products


def get_low_stock_products(threshold=None):
    """Get products with low stock"""
    from .models import Product
    
    queryset = Product.objects.filter(
        is_active=True,
        track_inventory=True
    )
    
    if threshold:
        queryset = queryset.filter(stock_quantity__lte=threshold)
    else:
        # Use each product's individual threshold
        queryset = queryset.extra(
            where=["stock_quantity <= low_stock_threshold"]
        )
    
    return queryset.order_by('stock_quantity')


def calculate_bundle_discount(products, discount_percentage=10):
    """Calculate bundle discount for cross-sell products"""
    total_price = sum(product.effective_price for product in products)
    discount_amount = total_price * (discount_percentage / 100)
    final_price = total_price - discount_amount
    
    return {
        'original_total': total_price,
        'discount_amount': discount_amount,
        'final_total': final_price,
        'discount_percentage': discount_percentage
    }


def get_product_performance_metrics(product, days=30):
    """Get comprehensive performance metrics for a product"""
    from .models import ProductView, OrderItem
    
    cutoff_date = timezone.now() - timedelta(days=days)
    
    # Views
    total_views = product.views.count()
    recent_views = product.views.filter(viewed_at__gte=cutoff_date).count()
    
    # Orders
    total_orders = product.orderitem_set.count()
    recent_orders = product.orderitem_set.filter(created_at__gte=cutoff_date).count()
    
    # Revenue
    total_revenue = product.orderitem_set.aggregate(
        revenue=Sum('total_price')
    )['revenue'] or 0
    
    recent_revenue = product.orderitem_set.filter(
        created_at__gte=cutoff_date
    ).aggregate(
        revenue=Sum('total_price')
    )['revenue'] or 0
    
    # Conversion rate
    conversion_rate = (total_orders / total_views * 100) if total_views > 0 else 0
    recent_conversion_rate = (recent_orders / recent_views * 100) if recent_views > 0 else 0
    
    return {
        'total_views': total_views,
        'recent_views': recent_views,
        'total_orders': total_orders,
        'recent_orders': recent_orders,
        'total_revenue': float(total_revenue),
        'recent_revenue': float(recent_revenue),
        'conversion_rate': round(conversion_rate, 2),
        'recent_conversion_rate': round(recent_conversion_rate, 2),
        'popularity_score': product.popularity_score,
        'rating_average': product.rating_average,
        'rating_count': product.rating_count,
    }
