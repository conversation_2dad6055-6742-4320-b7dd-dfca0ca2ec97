"""
Product API Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Avg, Count, Sum
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timedelta
import logging

from .models import (
    Category, Product, Customer, Cart, CartItem,
    Order, OrderItem, ProductReview, ProductView
)
from .serializers import (
    CategorySerializer, ProductListSerializer, ProductDetailSerializer,
    CustomerSerializer, CartSerializer, CartItemSerializer, OrderSerializer,
    ProductReviewSerializer, ProductViewSerializer, ProductSearchSerializer,
    BulkCartUpdateSerializer
)
from .utils import get_client_ip

logger = logging.getLogger(__name__)


class CategoryViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for Category management
    Provides list and detail views for product categories
    """
    queryset = Category.objects.filter(is_active=True)
    serializer_class = CategorySerializer
    lookup_field = 'slug'
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    @action(detail=True, methods=['get'])
    def products(self, request, slug=None):
        """Get products in this category"""
        category = self.get_object()
        products = Product.objects.filter(
            category=category,
            is_active=True
        ).select_related('category')

        # Apply search and filtering
        search_query = request.query_params.get('search', '')
        if search_query:
            products = products.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query)
            )

        # Pagination
        page = self.paginate_queryset(products)
        if page is not None:
            serializer = ProductListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ProductListSerializer(products, many=True, context={'request': request})
        return Response(serializer.data)


class ProductViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Product management
    Provides CRUD operations for products with advanced filtering and search
    """
    queryset = Product.objects.filter(is_active=True).select_related('category')
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'is_featured', 'is_digital']
    search_fields = ['name', 'description', 'sku']
    ordering_fields = ['name', 'price', 'created_at', 'popularity_score', 'rating_average']
    ordering = ['-created_at']
    lookup_field = 'slug'

    def get_serializer_class(self):
        if self.action == 'list':
            return ProductListSerializer
        return ProductDetailSerializer

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action in ['list', 'retrieve']:
            permission_classes = [IsAuthenticatedOrReadOnly]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

    def retrieve(self, request, *args, **kwargs):
        """Override retrieve to track product views"""
        instance = self.get_object()

        # Track product view
        self._track_product_view(request, instance)

        serializer = self.get_serializer(instance)
        return Response(serializer.data)

    def _track_product_view(self, request, product):
        """Track product view for analytics"""
        try:
            customer = None
            session_key = request.session.session_key

            if request.user.is_authenticated:
                customer = getattr(request.user, 'customer_profile', None)

            ProductView.objects.create(
                product=product,
                customer=customer,
                session_key=session_key or '',
                ip_address=get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                referrer=request.META.get('HTTP_REFERER', '')
            )
        except Exception as e:
            logger.warning(f"Failed to track product view: {e}")

    @action(detail=False, methods=['get'])
    def search(self, request):
        """Advanced product search with filters"""
        serializer = ProductSearchSerializer(data=request.query_params)
        serializer.is_valid(raise_exception=True)

        queryset = self.get_queryset()

        # Apply search filters
        search_query = serializer.validated_data.get('q')
        if search_query:
            queryset = queryset.filter(
                Q(name__icontains=search_query) |
                Q(description__icontains=search_query) |
                Q(sku__icontains=search_query)
            )

        # Category filter
        category_slug = serializer.validated_data.get('category')
        if category_slug:
            queryset = queryset.filter(category__slug=category_slug)

        # Price filters
        min_price = serializer.validated_data.get('min_price')
        max_price = serializer.validated_data.get('max_price')
        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)

        # Stock filter
        in_stock = serializer.validated_data.get('in_stock')
        if in_stock:
            queryset = queryset.filter(
                Q(track_inventory=False) |
                Q(track_inventory=True, stock_quantity__gt=0)
            )

        # Featured filter
        featured = serializer.validated_data.get('featured')
        if featured is not None:
            queryset = queryset.filter(is_featured=featured)

        # Sorting
        sort_by = serializer.validated_data.get('sort_by', '-created_at')
        queryset = queryset.order_by(sort_by)

        # Pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ProductListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ProductListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def featured(self, request):
        """Get featured products"""
        queryset = self.get_queryset().filter(is_featured=True)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = ProductListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ProductListSerializer(queryset, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def trending(self, request):
        """Get trending products based on recent views and purchases"""
        days = int(request.query_params.get('days', 7))
        cutoff_date = timezone.now() - timedelta(days=days)

        # Get products with high recent activity
        trending_products = Product.objects.filter(
            is_active=True
        ).annotate(
            recent_views=Count('views', filter=Q(views__viewed_at__gte=cutoff_date)),
            recent_orders=Count('orderitem', filter=Q(orderitem__created_at__gte=cutoff_date))
        ).filter(
            Q(recent_views__gt=0) | Q(recent_orders__gt=0)
        ).order_by('-recent_views', '-recent_orders', '-popularity_score')

        page = self.paginate_queryset(trending_products)
        if page is not None:
            serializer = ProductListSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ProductListSerializer(trending_products, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def reviews(self, request, slug=None):
        """Get reviews for a product"""
        product = self.get_object()
        reviews = product.reviews.filter(is_approved=True).select_related('customer__user')

        page = self.paginate_queryset(reviews)
        if page is not None:
            serializer = ProductReviewSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = ProductReviewSerializer(reviews, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated])
    def add_review(self, request, slug=None):
        """Add a review for a product"""
        product = self.get_object()

        # Check if user has already reviewed this product
        customer = getattr(request.user, 'customer_profile', None)
        if not customer:
            return Response(
                {'error': 'Customer profile required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        existing_review = ProductReview.objects.filter(
            product=product,
            customer=customer
        ).first()

        if existing_review:
            return Response(
                {'error': 'You have already reviewed this product'},
                status=status.HTTP_400_BAD_REQUEST
            )

        serializer = ProductReviewSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(product=product, customer=customer)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class CartViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Shopping Cart management
    """
    serializer_class = CartSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        if self.request.user.is_authenticated:
            customer = getattr(self.request.user, 'customer_profile', None)
            if customer:
                return Cart.objects.filter(customer=customer)
        return Cart.objects.filter(session_key=self.request.session.session_key)

    def get_or_create_cart(self):
        """Get or create cart for current user/session"""
        if self.request.user.is_authenticated:
            customer = getattr(self.request.user, 'customer_profile', None)
            if customer:
                cart, created = Cart.objects.get_or_create(customer=customer)
                return cart

        session_key = self.request.session.session_key
        if not session_key:
            self.request.session.create()
            session_key = self.request.session.session_key

        cart, created = Cart.objects.get_or_create(session_key=session_key)
        return cart

    @action(detail=False, methods=['get'])
    def current(self, request):
        """Get current user's cart"""
        cart = self.get_or_create_cart()
        serializer = self.get_serializer(cart)
        return Response(serializer.data)

    @action(detail=False, methods=['post'])
    def add_item(self, request):
        """Add item to cart"""
        cart = self.get_or_create_cart()

        serializer = CartItemSerializer(data=request.data)
        if serializer.is_valid():
            product = serializer.validated_data['product']
            quantity = serializer.validated_data['quantity']

            # Check if item already exists in cart
            cart_item, created = CartItem.objects.get_or_create(
                cart=cart,
                product=product,
                defaults={'quantity': quantity, 'unit_price': product.price}
            )

            if not created:
                cart_item.quantity += quantity
                cart_item.save()

            return Response(
                CartItemSerializer(cart_item, context={'request': request}).data,
                status=status.HTTP_201_CREATED
            )
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['put'])
    def update_item(self, request):
        """Update cart item quantity"""
        cart = self.get_or_create_cart()
        product_id = request.data.get('product_id')
        quantity = request.data.get('quantity', 0)

        if not product_id:
            return Response(
                {'error': 'product_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            cart_item = CartItem.objects.get(cart=cart, product_id=product_id)
            if quantity <= 0:
                cart_item.delete()
                return Response({'message': 'Item removed from cart'})
            else:
                cart_item.quantity = quantity
                cart_item.save()
                return Response(
                    CartItemSerializer(cart_item, context={'request': request}).data
                )
        except CartItem.DoesNotExist:
            return Response(
                {'error': 'Item not found in cart'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['delete'])
    def remove_item(self, request):
        """Remove item from cart"""
        cart = self.get_or_create_cart()
        product_id = request.data.get('product_id')

        if not product_id:
            return Response(
                {'error': 'product_id is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            cart_item = CartItem.objects.get(cart=cart, product_id=product_id)
            cart_item.delete()
            return Response({'message': 'Item removed from cart'})
        except CartItem.DoesNotExist:
            return Response(
                {'error': 'Item not found in cart'},
                status=status.HTTP_404_NOT_FOUND
            )

    @action(detail=False, methods=['post'])
    def bulk_update(self, request):
        """Bulk update cart items"""
        cart = self.get_or_create_cart()

        serializer = BulkCartUpdateSerializer(data=request.data)
        if serializer.is_valid():
            items = serializer.validated_data['items']
            updated_items = []

            for item_data in items:
                product_id = item_data['product_id']
                quantity = int(item_data['quantity'])

                try:
                    product = Product.objects.get(id=product_id, is_active=True)
                    cart_item, created = CartItem.objects.get_or_create(
                        cart=cart,
                        product=product,
                        defaults={'quantity': quantity, 'unit_price': product.price}
                    )

                    if not created:
                        if quantity <= 0:
                            cart_item.delete()
                            continue
                        else:
                            cart_item.quantity = quantity
                            cart_item.save()

                    updated_items.append(cart_item)

                except Product.DoesNotExist:
                    continue

            return Response({
                'message': f'Updated {len(updated_items)} items',
                'items': CartItemSerializer(updated_items, many=True, context={'request': request}).data
            })
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['delete'])
    def clear(self, request):
        """Clear all items from cart"""
        cart = self.get_or_create_cart()
        cart.items.all().delete()
        return Response({'message': 'Cart cleared'})


class OrderViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for Order management (read-only for customers)
    """
    serializer_class = OrderSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['created_at', 'total_amount', 'status']
    ordering = ['-created_at']

    def get_queryset(self):
        customer = getattr(self.request.user, 'customer_profile', None)
        if customer:
            return Order.objects.filter(customer=customer).select_related('customer__user')
        return Order.objects.none()

    @action(detail=True, methods=['get'])
    def items(self, request, pk=None):
        """Get order items"""
        order = self.get_object()
        items = order.items.all().select_related('product')

        serializer = OrderItemSerializer(items, many=True, context={'request': request})
        return Response(serializer.data)


class CustomerViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Customer profile management
    """
    serializer_class = CustomerSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        return Customer.objects.filter(user=self.request.user)

    def get_object(self):
        customer, created = Customer.objects.get_or_create(user=self.request.user)
        return customer

    @action(detail=False, methods=['get'])
    def profile(self, request):
        """Get current user's profile"""
        customer = self.get_object()
        serializer = self.get_serializer(customer)
        return Response(serializer.data)

    @action(detail=False, methods=['put', 'patch'])
    def update_profile(self, request):
        """Update current user's profile"""
        customer = self.get_object()
        serializer = self.get_serializer(customer, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            return Response(serializer.data)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def orders(self, request):
        """Get customer's orders"""
        customer = self.get_object()
        orders = customer.orders.all().order_by('-created_at')

        page = self.paginate_queryset(orders)
        if page is not None:
            serializer = OrderSerializer(page, many=True, context={'request': request})
            return self.get_paginated_response(serializer.data)

        serializer = OrderSerializer(orders, many=True, context={'request': request})
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def purchase_history(self, request):
        """Get customer's purchase history with analytics"""
        customer = self.get_object()

        # Get purchase statistics
        orders = customer.orders.filter(status__in=['completed', 'delivered'])
        total_orders = orders.count()
        total_spent = orders.aggregate(total=Sum('total_amount'))['total'] or 0

        # Get frequently purchased products
        frequent_products = Product.objects.filter(
            orderitem__order__customer=customer,
            orderitem__order__status__in=['completed', 'delivered']
        ).annotate(
            purchase_count=Count('orderitem')
        ).order_by('-purchase_count')[:10]

        return Response({
            'total_orders': total_orders,
            'total_spent': total_spent,
            'average_order_value': total_spent / total_orders if total_orders > 0 else 0,
            'frequent_products': ProductListSerializer(
                frequent_products, many=True, context={'request': request}
            ).data
        })
