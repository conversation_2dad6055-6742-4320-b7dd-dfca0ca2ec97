"""
Hyperparameter optimization for cross-selling models
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from itertools import product
import time
from pathlib import Path
from django.conf import settings

from .model_evaluator import CrossSellModelEvaluator
from .ml_engine import AssociationRuleMiner

logger = logging.getLogger(__name__)


class HyperparameterTuner:
    """
    Hyperparameter optimization for association rule mining models
    """
    
    def __init__(self, 
                 evaluation_metric: str = 'overall_score',
                 cv_folds: int = 3,
                 random_state: int = 42):
        """
        Initialize hyperparameter tuner
        
        Args:
            evaluation_metric: Primary metric for optimization
            cv_folds: Number of cross-validation folds
            random_state: Random seed for reproducibility
        """
        self.evaluation_metric = evaluation_metric
        self.cv_folds = cv_folds
        self.random_state = random_state
        self.tuning_results = []
        
    def define_parameter_grid(self) -> Dict:
        """
        Define hyperparameter search space
        
        Returns:
            Dictionary with parameter ranges
        """
        parameter_grid = {
            'min_support': [0.001, 0.005, 0.01, 0.02, 0.03],
            'min_confidence': [0.1, 0.2, 0.3, 0.4, 0.5],
            'max_itemsets': [200, 500, 1000, 2000]
        }
        
        logger.info(f"Parameter grid defined: {parameter_grid}")
        return parameter_grid
    
    def grid_search(self, 
                   binary_matrix: pd.DataFrame,
                   parameter_grid: Dict = None,
                   max_combinations: int = 50) -> Dict:
        """
        Perform grid search over hyperparameters
        
        Args:
            binary_matrix: Binary transaction matrix
            parameter_grid: Parameter search space
            max_combinations: Maximum parameter combinations to try
            
        Returns:
            Best parameters and results
        """
        if parameter_grid is None:
            parameter_grid = self.define_parameter_grid()
        
        logger.info("Starting grid search hyperparameter optimization")
        
        # Generate all parameter combinations
        param_names = list(parameter_grid.keys())
        param_values = list(parameter_grid.values())
        all_combinations = list(product(*param_values))
        
        # Limit combinations if too many
        if len(all_combinations) > max_combinations:
            logger.info(f"Limiting to {max_combinations} random combinations out of {len(all_combinations)}")
            np.random.seed(self.random_state)
            selected_indices = np.random.choice(len(all_combinations), max_combinations, replace=False)
            all_combinations = [all_combinations[i] for i in selected_indices]
        
        logger.info(f"Testing {len(all_combinations)} parameter combinations")
        
        best_score = -1
        best_params = None
        best_results = None
        
        for i, param_combination in enumerate(all_combinations):
            # Create parameter dictionary
            params = dict(zip(param_names, param_combination))
            
            logger.info(f"Testing combination {i+1}/{len(all_combinations)}: {params}")
            
            try:
                # Evaluate this parameter combination
                results = self._evaluate_parameters(binary_matrix, params)
                
                # Track results
                self.tuning_results.append({
                    'params': params,
                    'results': results,
                    'score': results[self.evaluation_metric]
                })
                
                # Check if this is the best so far
                if results[self.evaluation_metric] > best_score:
                    best_score = results[self.evaluation_metric]
                    best_params = params.copy()
                    best_results = results.copy()
                    
                    logger.info(f"New best score: {best_score:.4f} with params: {best_params}")
                
            except Exception as e:
                logger.error(f"Error evaluating parameters {params}: {e}")
                continue
        
        optimization_results = {
            'best_params': best_params,
            'best_score': best_score,
            'best_results': best_results,
            'all_results': self.tuning_results,
            'total_combinations_tested': len(self.tuning_results)
        }
        
        logger.info(f"Grid search completed. Best score: {best_score:.4f}")
        return optimization_results
    
    def random_search(self, 
                     binary_matrix: pd.DataFrame,
                     n_iterations: int = 20,
                     parameter_ranges: Dict = None) -> Dict:
        """
        Perform random search over hyperparameters
        
        Args:
            binary_matrix: Binary transaction matrix
            n_iterations: Number of random combinations to try
            parameter_ranges: Parameter ranges for random sampling
            
        Returns:
            Best parameters and results
        """
        if parameter_ranges is None:
            parameter_ranges = {
                'min_support': (0.001, 0.05),
                'min_confidence': (0.1, 0.7),
                'max_itemsets': (100, 2000)
            }
        
        logger.info(f"Starting random search with {n_iterations} iterations")
        
        best_score = -1
        best_params = None
        best_results = None
        
        np.random.seed(self.random_state)
        
        for i in range(n_iterations):
            # Generate random parameters
            params = {}
            for param_name, (min_val, max_val) in parameter_ranges.items():
                if param_name == 'max_itemsets':
                    # Integer parameter
                    params[param_name] = np.random.randint(min_val, max_val + 1)
                else:
                    # Float parameter
                    params[param_name] = np.random.uniform(min_val, max_val)
            
            logger.info(f"Testing iteration {i+1}/{n_iterations}: {params}")
            
            try:
                # Evaluate this parameter combination
                results = self._evaluate_parameters(binary_matrix, params)
                
                # Track results
                self.tuning_results.append({
                    'params': params,
                    'results': results,
                    'score': results[self.evaluation_metric]
                })
                
                # Check if this is the best so far
                if results[self.evaluation_metric] > best_score:
                    best_score = results[self.evaluation_metric]
                    best_params = params.copy()
                    best_results = results.copy()
                    
                    logger.info(f"New best score: {best_score:.4f} with params: {best_params}")
                
            except Exception as e:
                logger.error(f"Error evaluating parameters {params}: {e}")
                continue
        
        optimization_results = {
            'best_params': best_params,
            'best_score': best_score,
            'best_results': best_results,
            'all_results': self.tuning_results,
            'total_iterations': len(self.tuning_results)
        }
        
        logger.info(f"Random search completed. Best score: {best_score:.4f}")
        return optimization_results
    
    def _evaluate_parameters(self, binary_matrix: pd.DataFrame, params: Dict) -> Dict:
        """
        Evaluate a specific parameter combination using cross-validation
        
        Args:
            binary_matrix: Binary transaction matrix
            params: Parameters to evaluate
            
        Returns:
            Evaluation results
        """
        evaluator = CrossSellModelEvaluator(test_size=0.2, random_state=self.random_state)
        
        # Run evaluation
        results = evaluator.comprehensive_evaluation(binary_matrix, params)
        
        # Extract key metrics for optimization
        evaluation_summary = {
            'overall_score': results['overall_score'],
            'precision': results['accuracy_metrics']['precision'],
            'recall': results['accuracy_metrics']['recall'],
            'f1_score': results['accuracy_metrics']['f1_score'],
            'hit_rate': results['accuracy_metrics']['hit_rate'],
            'cross_sell_rate': results['business_metrics']['cross_sell_rate'],
            'avg_confidence': results['rule_quality']['avg_confidence'],
            'avg_lift': results['rule_quality']['avg_lift'],
            'total_rules': results['rule_quality']['total_rules'],
            'training_time': results['training_results']['training_duration']
        }
        
        return evaluation_summary
    
    def bayesian_optimization(self, 
                            binary_matrix: pd.DataFrame,
                            n_iterations: int = 30) -> Dict:
        """
        Perform Bayesian optimization (simplified version)
        
        Args:
            binary_matrix: Binary transaction matrix
            n_iterations: Number of optimization iterations
            
        Returns:
            Best parameters and results
        """
        logger.info("Starting Bayesian optimization (simplified)")
        
        # Start with random search to get initial points
        initial_results = self.random_search(binary_matrix, n_iterations=10)
        
        # Use the best parameters as starting point for local optimization
        best_params = initial_results['best_params']
        
        # Define local search around best parameters
        local_ranges = {
            'min_support': (max(0.001, best_params['min_support'] * 0.5), 
                           min(0.1, best_params['min_support'] * 2.0)),
            'min_confidence': (max(0.05, best_params['min_confidence'] * 0.7), 
                              min(0.8, best_params['min_confidence'] * 1.3)),
            'max_itemsets': (max(100, int(best_params['max_itemsets'] * 0.5)), 
                            min(3000, int(best_params['max_itemsets'] * 1.5)))
        }
        
        # Continue with focused random search
        additional_results = self.random_search(
            binary_matrix, 
            n_iterations=n_iterations-10, 
            parameter_ranges=local_ranges
        )
        
        # Return the best overall results
        all_results = self.tuning_results
        best_result = max(all_results, key=lambda x: x['score'])
        
        return {
            'best_params': best_result['params'],
            'best_score': best_result['score'],
            'best_results': best_result['results'],
            'all_results': all_results,
            'optimization_method': 'bayesian_simplified'
        }
    
    def analyze_parameter_importance(self) -> Dict:
        """
        Analyze the importance of different parameters
        
        Returns:
            Parameter importance analysis
        """
        if not self.tuning_results:
            logger.warning("No tuning results available for analysis")
            return {}
        
        logger.info("Analyzing parameter importance")
        
        # Convert results to DataFrame for analysis
        data = []
        for result in self.tuning_results:
            row = result['params'].copy()
            row['score'] = result['score']
            data.append(row)
        
        df = pd.DataFrame(data)
        
        # Calculate correlations
        correlations = {}
        for param in ['min_support', 'min_confidence', 'max_itemsets']:
            if param in df.columns:
                correlation = df[param].corr(df['score'])
                correlations[param] = correlation
        
        # Find parameter ranges for best performing models
        top_10_percent = df.nlargest(max(1, len(df) // 10), 'score')
        
        best_ranges = {}
        for param in ['min_support', 'min_confidence', 'max_itemsets']:
            if param in top_10_percent.columns:
                best_ranges[param] = {
                    'min': top_10_percent[param].min(),
                    'max': top_10_percent[param].max(),
                    'mean': top_10_percent[param].mean(),
                    'std': top_10_percent[param].std()
                }
        
        importance_analysis = {
            'parameter_correlations': correlations,
            'best_parameter_ranges': best_ranges,
            'total_experiments': len(df),
            'best_score_achieved': df['score'].max(),
            'score_statistics': {
                'mean': df['score'].mean(),
                'std': df['score'].std(),
                'min': df['score'].min(),
                'max': df['score'].max()
            }
        }
        
        logger.info(f"Parameter importance analysis completed: {importance_analysis}")
        return importance_analysis
    
    def save_tuning_results(self, output_dir: str = None):
        """Save hyperparameter tuning results"""
        if output_dir is None:
            output_dir = Path(settings.BASE_DIR) / 'data' / 'processed' / 'hyperparameter_tuning'
        else:
            output_dir = Path(output_dir)
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Save detailed results
        import json
        timestamp = int(time.time())
        results_file = output_dir / f"tuning_results_{timestamp}.json"
        
        # Convert numpy types for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            return obj
        
        json_results = json.loads(json.dumps(self.tuning_results, default=convert_numpy))
        
        with open(results_file, 'w') as f:
            json.dump(json_results, f, indent=2)
        
        # Save summary CSV
        summary_data = []
        for result in self.tuning_results:
            row = result['params'].copy()
            row.update({
                'score': result['score'],
                'precision': result['results'].get('precision', 0),
                'recall': result['results'].get('recall', 0),
                'f1_score': result['results'].get('f1_score', 0)
            })
            summary_data.append(row)
        
        summary_df = pd.DataFrame(summary_data)
        summary_file = output_dir / f"tuning_summary_{timestamp}.csv"
        summary_df.to_csv(summary_file, index=False)
        
        logger.info(f"Saved tuning results to {results_file} and {summary_file}")
        
        return {
            'results_file': str(results_file),
            'summary_file': str(summary_file),
            'total_experiments': len(self.tuning_results)
        }
