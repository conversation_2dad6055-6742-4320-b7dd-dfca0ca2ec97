"""
Django management command to create sample products in database
"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import pandas as pd
from pathlib import Path
from decimal import Decimal
import uuid

from products.models import Product, Category


class Command(BaseCommand):
    help = 'Create sample products in database from processed data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--clear-existing',
            action='store_true',
            help='Clear existing products before creating new ones',
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating sample products in database...')
        
        try:
            # Clear existing products if requested
            if options['clear_existing']:
                Product.objects.all().delete()
                Category.objects.all().delete()
                self.stdout.write('Cleared existing products and categories')
            
            # Load sample data
            data_dir = Path(settings.BASE_DIR) / 'data' / 'raw'
            
            # Load categories data
            aisles_df = pd.read_csv(data_dir / 'aisles.csv')
            departments_df = pd.read_csv(data_dir / 'departments.csv')
            products_df = pd.read_csv(data_dir / 'products.csv')
            
            # Create categories (using aisles as categories)
            category_mapping = {}
            for _, row in aisles_df.iterrows():
                category, created = Category.objects.get_or_create(
                    name=row['aisle'],
                    defaults={
                        'slug': row['aisle'].lower().replace(' ', '-'),
                        'description': f"Products in {row['aisle']} category"
                    }
                )
                category_mapping[row['aisle_id']] = category
            
            self.stdout.write(f'Created {len(category_mapping)} categories')
            
            # Create products
            products_created = 0
            for _, row in products_df.iterrows():
                # Map integer ID to UUID for consistency
                product_uuid = uuid.UUID(int=row['product_id'])
                
                category = category_mapping.get(row['aisle_id'])
                
                product, created = Product.objects.get_or_create(
                    id=product_uuid,
                    defaults={
                        'name': row['product_name'],
                        'slug': f"product-{row['product_id']}",
                        'description': f"Sample product {row['product_name']}",
                        'price': Decimal('9.99'),  # Default price
                        'sku': f"SKU-{row['product_id']:06d}",
                        'category': category,
                        'is_active': True,
                        'stock_quantity': 100,
                    }
                )
                
                if created:
                    products_created += 1
            
            self.stdout.write(
                self.style.SUCCESS(f'Created {products_created} products in database')
            )
            
            # Update the product ID mapping for association rules
            self._create_id_mapping()
            
        except Exception as e:
            raise CommandError(f'Failed to create sample products: {e}')
    
    def _create_id_mapping(self):
        """Create a mapping file for integer IDs to UUIDs"""
        mapping_file = Path(settings.BASE_DIR) / 'data' / 'processed' / 'product_id_mapping.csv'
        
        # Get all products and create mapping
        products = Product.objects.all().values('id', 'name')
        
        mapping_data = []
        for product in products:
            # Extract integer from UUID
            integer_id = product['id'].int
            mapping_data.append({
                'integer_id': integer_id,
                'uuid_id': str(product['id']),
                'product_name': product['name']
            })
        
        mapping_df = pd.DataFrame(mapping_data)
        mapping_df.to_csv(mapping_file, index=False)
        
        self.stdout.write(f'Created product ID mapping at {mapping_file}')
