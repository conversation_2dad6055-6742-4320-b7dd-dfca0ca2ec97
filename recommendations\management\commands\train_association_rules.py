"""
Django management command to train association rule models
"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import logging
import time

from recommendations.ml_engine import AssociationRuleMiner
from recommendations.recommendation_engine import CrossSellRecommendationEngine

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Train association rule models for cross-selling recommendations'

    def add_arguments(self, parser):
        parser.add_argument(
            '--min-support',
            type=float,
            default=0.01,
            help='Minimum support threshold (default: 0.01)',
        )
        parser.add_argument(
            '--min-confidence',
            type=float,
            default=0.5,
            help='Minimum confidence threshold (default: 0.5)',
        )
        parser.add_argument(
            '--max-itemsets',
            type=int,
            default=1000,
            help='Maximum number of itemsets to consider (default: 1000)',
        )
        parser.add_argument(
            '--data-source',
            type=str,
            default='processed',
            help='Data source: "processed" or file path (default: processed)',
        )
        parser.add_argument(
            '--model-version',
            type=str,
            help='Model version identifier (auto-generated if not provided)',
        )
        parser.add_argument(
            '--generate-recommendations',
            action='store_true',
            help='Generate product recommendations after training',
        )
        parser.add_argument(
            '--test-recommendations',
            action='store_true',
            help='Test recommendations with sample products',
        )

    def handle(self, *args, **options):
        start_time = time.time()
        
        self.stdout.write(
            self.style.SUCCESS('Starting association rule mining training...')
        )
        
        try:
            # Initialize the miner
            miner = AssociationRuleMiner(
                min_support=options['min_support'],
                min_confidence=options['min_confidence'],
                max_itemsets=options['max_itemsets'],
                model_version=options.get('model_version')
            )
            
            # Train the model
            self.stdout.write('Training association rule model...')
            results = miner.train(data_source=options['data_source'])
            
            # Print training results
            self._print_training_results(results)
            
            # Generate product recommendations if requested
            if options['generate_recommendations']:
                self.stdout.write('\nGenerating product recommendations...')
                engine = CrossSellRecommendationEngine(model_version=results['model_version'])
                recommendations_count = engine.generate_product_recommendations_batch(
                    force_regenerate=True
                )
                self.stdout.write(
                    self.style.SUCCESS(f'Generated {recommendations_count} product recommendations')
                )
            
            # Test recommendations if requested
            if options['test_recommendations']:
                self.stdout.write('\nTesting recommendations...')
                self._test_recommendations(results['model_version'])
            
            elapsed_time = time.time() - start_time
            self.stdout.write(
                self.style.SUCCESS(
                    f'\nTraining completed successfully in {elapsed_time:.2f} seconds!'
                )
            )
            
        except Exception as e:
            logger.error(f"Error during training: {e}")
            raise CommandError(f'Training failed: {e}')

    def _print_training_results(self, results):
        """Print training results"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.WARNING('TRAINING RESULTS'))
        self.stdout.write('='*60)
        
        stats = results['training_stats']
        
        self.stdout.write(f'Model Version: {results["model_version"]}')
        self.stdout.write(f'Status: {results["status"]}')
        self.stdout.write(f'Training Duration: {stats["training_duration"]:.2f} seconds')
        self.stdout.write(f'Total Transactions: {stats["total_transactions"]:,}')
        self.stdout.write(f'Total Products: {stats["total_products"]:,}')
        self.stdout.write(f'Frequent Itemsets Found: {stats["frequent_itemsets_count"]:,}')
        self.stdout.write(f'Association Rules Generated: {stats["association_rules_count"]:,}')
        
        # Show top rules
        if 'association_rules' in results and not results['association_rules'].empty:
            self.stdout.write('\nTop 10 Association Rules:')
            self.stdout.write('-' * 60)
            
            top_rules = results['association_rules'].head(10)
            for idx, (_, rule) in enumerate(top_rules.iterrows(), 1):
                antecedents = ', '.join([str(x) for x in rule['antecedents']])
                consequents = ', '.join([str(x) for x in rule['consequents']])
                
                self.stdout.write(
                    f'{idx:2d}. [{antecedents}] → [{consequents}]'
                )
                self.stdout.write(
                    f'    Support: {rule["support"]:.3f}, '
                    f'Confidence: {rule["confidence"]:.3f}, '
                    f'Lift: {rule["lift"]:.3f}'
                )
        
        # Show itemset size distribution
        if 'frequent_itemsets' in results and not results['frequent_itemsets'].empty:
            self.stdout.write('\nItemset Size Distribution:')
            self.stdout.write('-' * 30)
            
            size_dist = results['frequent_itemsets']['itemset_size'].value_counts().sort_index()
            for size, count in size_dist.items():
                self.stdout.write(f'Size {size}: {count:,} itemsets')

    def _test_recommendations(self, model_version):
        """Test recommendations with sample products"""
        try:
            from products.models import Product
            
            engine = CrossSellRecommendationEngine(model_version=model_version)
            
            # Get some sample products
            sample_products = Product.objects.filter(is_active=True)[:5]
            
            if not sample_products:
                self.stdout.write(self.style.WARNING('No active products found for testing'))
                return
            
            self.stdout.write('\n' + '='*60)
            self.stdout.write(self.style.WARNING('RECOMMENDATION TESTING'))
            self.stdout.write('='*60)
            
            for product in sample_products:
                self.stdout.write(f'\nTesting recommendations for: {product.name} (ID: {product.id})')
                self.stdout.write('-' * 50)
                
                recommendations = engine.get_product_recommendations(
                    [product.id], 
                    max_recommendations=5
                )
                
                if recommendations:
                    for i, rec in enumerate(recommendations, 1):
                        self.stdout.write(
                            f'{i}. {rec["product_name"]} '
                            f'(Score: {rec["score"]:.3f}, '
                            f'Confidence: {rec["max_confidence"]:.3f})'
                        )
                else:
                    self.stdout.write('No recommendations found')
            
            # Test bundle suggestions
            if len(sample_products) >= 2:
                self.stdout.write('\n' + '-'*60)
                self.stdout.write('Testing Bundle Suggestions:')
                self.stdout.write('-'*60)
                
                test_products = sample_products[:2]
                product_ids = [p.id for p in test_products]
                product_names = [p.name for p in test_products]
                
                self.stdout.write(f'Base products: {", ".join(product_names)}')
                
                bundles = engine.get_bundle_suggestions(product_ids, max_bundles=3)
                
                if bundles:
                    for i, bundle in enumerate(bundles, 1):
                        rec_product = bundle['recommended_product']
                        self.stdout.write(
                            f'{i}. Add "{rec_product["name"]}" '
                            f'- Save ${bundle["savings"]:.2f} '
                            f'(Final: ${bundle["final_price"]:.2f})'
                        )
                else:
                    self.stdout.write('No bundle suggestions found')
                    
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Error during recommendation testing: {e}')
            )
