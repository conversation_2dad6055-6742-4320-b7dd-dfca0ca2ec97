"""
Django management command for comprehensive cross-selling model training
"""
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings
import logging
import time

from recommendations.training_pipeline import CrossSellTrainingPipeline

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Train cross-selling model with comprehensive pipeline including optimization and evaluation'

    def add_arguments(self, parser):
        # Data source
        parser.add_argument(
            '--data-source',
            type=str,
            default='processed',
            help='Data source: "processed" or file path (default: processed)',
        )
        
        # Baseline model parameters
        parser.add_argument(
            '--baseline-min-support',
            type=float,
            default=0.01,
            help='Baseline minimum support threshold (default: 0.01)',
        )
        parser.add_argument(
            '--baseline-min-confidence',
            type=float,
            default=0.3,
            help='Baseline minimum confidence threshold (default: 0.3)',
        )
        parser.add_argument(
            '--baseline-max-itemsets',
            type=int,
            default=500,
            help='Baseline maximum itemsets (default: 500)',
        )
        
        # Optimization settings
        parser.add_argument(
            '--skip-optimization',
            action='store_true',
            help='Skip hyperparameter optimization',
        )
        parser.add_argument(
            '--optimization-method',
            type=str,
            choices=['grid_search', 'random_search', 'bayesian'],
            default='random_search',
            help='Optimization method (default: random_search)',
        )
        parser.add_argument(
            '--max-combinations',
            type=int,
            default=20,
            help='Maximum parameter combinations for grid search (default: 20)',
        )
        parser.add_argument(
            '--optimization-iterations',
            type=int,
            default=15,
            help='Number of iterations for random/bayesian search (default: 15)',
        )
        
        # Evaluation settings
        parser.add_argument(
            '--skip-evaluation',
            action='store_true',
            help='Skip comprehensive model evaluation',
        )
        
        # Output settings
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Enable verbose output',
        )

    def handle(self, *args, **options):
        start_time = time.time()
        
        if options['verbose']:
            logging.getLogger('recommendations').setLevel(logging.DEBUG)
        
        self.stdout.write(
            self.style.SUCCESS('Starting comprehensive cross-selling model training pipeline...')
        )
        
        try:
            # Prepare baseline parameters
            baseline_params = {
                'min_support': options['baseline_min_support'],
                'min_confidence': options['baseline_min_confidence'],
                'max_itemsets': options['baseline_max_itemsets']
            }
            
            # Prepare optimization configuration
            optimization_config = {
                'max_combinations': options['max_combinations'],
                'n_iterations': options['optimization_iterations']
            }
            
            # Initialize pipeline
            pipeline = CrossSellTrainingPipeline(
                auto_optimize=not options['skip_optimization'],
                optimization_method=options['optimization_method'],
                evaluation_enabled=not options['skip_evaluation']
            )
            
            # Print configuration
            self._print_configuration(baseline_params, optimization_config, options)
            
            # Run complete pipeline
            self.stdout.write('Running training pipeline...')
            results = pipeline.run_full_pipeline(
                data_source=options['data_source'],
                baseline_params=baseline_params,
                optimization_config=optimization_config
            )
            
            # Print results
            self._print_results(results)
            
            # Print recommendations
            recommendations = pipeline.get_model_recommendations()
            self._print_recommendations(recommendations)
            
            elapsed_time = time.time() - start_time
            self.stdout.write(
                self.style.SUCCESS(
                    f'\nTraining pipeline completed successfully in {elapsed_time:.2f} seconds!'
                )
            )
            
        except Exception as e:
            logger.error(f"Error during training pipeline: {e}")
            raise CommandError(f'Training pipeline failed: {e}')

    def _print_configuration(self, baseline_params, optimization_config, options):
        """Print pipeline configuration"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.WARNING('PIPELINE CONFIGURATION'))
        self.stdout.write('='*60)
        
        self.stdout.write(f'Data Source: {options["data_source"]}')
        self.stdout.write(f'Optimization Enabled: {not options["skip_optimization"]}')
        if not options['skip_optimization']:
            self.stdout.write(f'Optimization Method: {options["optimization_method"]}')
        self.stdout.write(f'Evaluation Enabled: {not options["skip_evaluation"]}')
        
        self.stdout.write('\nBaseline Parameters:')
        for key, value in baseline_params.items():
            self.stdout.write(f'  {key}: {value}')
        
        if not options['skip_optimization']:
            self.stdout.write('\nOptimization Configuration:')
            for key, value in optimization_config.items():
                self.stdout.write(f'  {key}: {value}')

    def _print_results(self, results):
        """Print training results"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.WARNING('TRAINING RESULTS'))
        self.stdout.write('='*60)
        
        # Data statistics
        if 'data_statistics' in results:
            stats = results['data_statistics']
            self.stdout.write('\nData Statistics:')
            self.stdout.write(f'  Total Transactions: {stats["total_transactions"]:,}')
            self.stdout.write(f'  Total Products: {stats["total_products"]:,}')
            self.stdout.write(f'  Data Sparsity: {stats["sparsity"]:.3f}')
            self.stdout.write(f'  Average Basket Size: {stats["avg_basket_size"]:.2f}')
        
        # Baseline model results
        if 'baseline_model' in results:
            baseline = results['baseline_model']
            self.stdout.write('\nBaseline Model:')
            self.stdout.write(f'  Model Version: {baseline["model_version"]}')
            self.stdout.write(f'  Training Duration: {baseline["training_stats"]["training_duration"]:.2f}s')
            self.stdout.write(f'  Frequent Itemsets: {baseline["training_stats"]["frequent_itemsets_count"]:,}')
            self.stdout.write(f'  Association Rules: {baseline["training_stats"]["association_rules_count"]:,}')
        
        # Optimization results
        if 'optimization_results' in results and results['optimization_results']:
            opt = results['optimization_results']
            self.stdout.write('\nHyperparameter Optimization:')
            self.stdout.write(f'  Best Score: {opt["best_score"]:.4f}')
            self.stdout.write(f'  Best Parameters:')
            for key, value in opt['best_params'].items():
                self.stdout.write(f'    {key}: {value}')
            self.stdout.write(f'  Total Experiments: {opt.get("total_combinations_tested", opt.get("total_iterations", 0))}')
        
        # Final model results
        if 'final_model' in results:
            final = results['final_model']
            self.stdout.write('\nFinal Optimized Model:')
            self.stdout.write(f'  Model Version: {final["model_version"]}')
            self.stdout.write(f'  Training Duration: {final["training_stats"]["training_duration"]:.2f}s')
            self.stdout.write(f'  Frequent Itemsets: {final["training_stats"]["frequent_itemsets_count"]:,}')
            self.stdout.write(f'  Association Rules: {final["training_stats"]["association_rules_count"]:,}')
        
        # Evaluation results
        if 'evaluation_results' in results and results['evaluation_results']:
            eval_res = results['evaluation_results']
            self.stdout.write('\nModel Evaluation:')
            self.stdout.write(f'  Overall Score: {eval_res["overall_score"]:.4f}')
            
            acc = eval_res['accuracy_metrics']
            self.stdout.write(f'  Precision: {acc["precision"]:.4f}')
            self.stdout.write(f'  Recall: {acc["recall"]:.4f}')
            self.stdout.write(f'  F1 Score: {acc["f1_score"]:.4f}')
            self.stdout.write(f'  Hit Rate: {acc["hit_rate"]:.4f}')
            
            bus = eval_res['business_metrics']
            self.stdout.write(f'  Cross-sell Rate: {bus["cross_sell_rate"]:.4f}')
            self.stdout.write(f'  Avg Revenue/Opportunity: ${bus["avg_revenue_per_opportunity"]:.2f}')
        
        # Recommendation generation
        if 'recommendation_stats' in results:
            rec_stats = results['recommendation_stats']
            self.stdout.write('\nRecommendation Generation:')
            self.stdout.write(f'  Recommendations Generated: {rec_stats["recommendations_generated"]:,}')
            self.stdout.write(f'  Generation Successful: {rec_stats["generation_successful"]}')
        
        # Model analysis
        if 'model_analysis' in results:
            analysis = results['model_analysis']
            if 'baseline_vs_optimized' in analysis:
                comp = analysis['baseline_vs_optimized']
                self.stdout.write('\nBaseline vs Optimized Comparison:')
                self.stdout.write(f'  Rules Improvement: {comp["rules_improvement"]:+,}')
                self.stdout.write(f'  Itemsets Improvement: {comp["itemsets_improvement"]:+,}')

    def _print_recommendations(self, recommendations):
        """Print model recommendations"""
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.WARNING('MODEL RECOMMENDATIONS'))
        self.stdout.write('='*60)
        
        for i, recommendation in enumerate(recommendations, 1):
            self.stdout.write(f'{i}. {recommendation}')
        
        self.stdout.write('\nNext Steps:')
        self.stdout.write('- Monitor recommendation performance in production')
        self.stdout.write('- Set up periodic model retraining')
        self.stdout.write('- Implement A/B testing for recommendation effectiveness')
        self.stdout.write('- Consider ensemble methods for improved accuracy')
