"""
Association Rule Mining Engine using MLxtend
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from pathlib import Path
from django.conf import settings
from django.utils import timezone
from decimal import Decimal

# MLxtend imports
from mlxtend.frequent_patterns import apriori, association_rules
from mlxtend.preprocessing import TransactionEncoder

# Django model imports
from .models import AssociationRule, FrequentItemset, ProductRecommendation, ModelTrainingLog
from products.models import Product

logger = logging.getLogger(__name__)


class AssociationRuleMiner:
    """
    Association Rule Mining engine using Apriori algorithm
    """
    
    def __init__(self, 
                 min_support: float = 0.01,
                 min_confidence: float = 0.5,
                 max_itemsets: int = 1000,
                 model_version: str = None):
        """
        Initialize the association rule miner
        
        Args:
            min_support: Minimum support threshold for frequent itemsets
            min_confidence: Minimum confidence threshold for association rules
            max_itemsets: Maximum number of itemsets to consider
            model_version: Version identifier for the model
        """
        self.min_support = min_support
        self.min_confidence = min_confidence
        self.max_itemsets = max_itemsets
        self.model_version = model_version or f"v{timezone.now().strftime('%Y%m%d_%H%M%S')}"
        
        # Storage for results
        self.frequent_itemsets = None
        self.association_rules_df = None
        self.training_log = None
        
    def load_transaction_data(self, data_source: str = 'processed') -> pd.DataFrame:
        """
        Load transaction data for mining
        
        Args:
            data_source: Source of data ('processed', 'database', or file path)
            
        Returns:
            Binary transaction matrix
        """
        logger.info(f"Loading transaction data from {data_source}")
        
        if data_source == 'processed':
            # Load from processed data files
            processed_dir = Path(settings.BASE_DIR) / 'data' / 'processed'
            matrix_path = processed_dir / 'filtered_binary_matrix.csv'
            
            if not matrix_path.exists():
                raise FileNotFoundError(f"Processed data not found at {matrix_path}")
            
            binary_matrix = pd.read_csv(matrix_path, index_col=0)
            logger.info(f"Loaded binary matrix: {binary_matrix.shape}")
            
        elif data_source == 'database':
            # Load from database (implement if needed)
            raise NotImplementedError("Database loading not yet implemented")
            
        else:
            # Load from custom file path
            binary_matrix = pd.read_csv(data_source, index_col=0)
            
        return binary_matrix
    
    def find_frequent_itemsets(self, binary_matrix: pd.DataFrame) -> pd.DataFrame:
        """
        Find frequent itemsets using Apriori algorithm
        
        Args:
            binary_matrix: Binary transaction matrix
            
        Returns:
            DataFrame with frequent itemsets
        """
        logger.info(f"Finding frequent itemsets with min_support={self.min_support}")
        
        # Convert to boolean type to avoid warning
        binary_matrix_bool = binary_matrix.astype(bool)

        # Apply Apriori algorithm
        frequent_itemsets = apriori(
            binary_matrix_bool,
            min_support=self.min_support,
            use_colnames=True,
            max_len=None,  # No limit on itemset length
            verbose=1
        )
        
        if frequent_itemsets.empty:
            logger.warning("No frequent itemsets found with current parameters")
            return frequent_itemsets
        
        # Limit number of itemsets if specified
        if self.max_itemsets and len(frequent_itemsets) > self.max_itemsets:
            # Sort by support and take top itemsets
            frequent_itemsets = frequent_itemsets.nlargest(self.max_itemsets, 'support')
            logger.info(f"Limited to top {self.max_itemsets} itemsets")
        
        # Add itemset size information
        frequent_itemsets['itemset_size'] = frequent_itemsets['itemsets'].apply(len)
        frequent_itemsets['transaction_count'] = (
            frequent_itemsets['support'] * len(binary_matrix)
        ).astype(int)
        
        logger.info(f"Found {len(frequent_itemsets)} frequent itemsets")
        logger.info(f"Itemset size distribution:\n{frequent_itemsets['itemset_size'].value_counts().sort_index()}")
        
        self.frequent_itemsets = frequent_itemsets
        return frequent_itemsets
    
    def generate_association_rules(self, frequent_itemsets: pd.DataFrame) -> pd.DataFrame:
        """
        Generate association rules from frequent itemsets
        
        Args:
            frequent_itemsets: DataFrame with frequent itemsets
            
        Returns:
            DataFrame with association rules
        """
        logger.info(f"Generating association rules with min_confidence={self.min_confidence}")
        
        if frequent_itemsets.empty:
            logger.warning("No frequent itemsets provided for rule generation")
            return pd.DataFrame()
        
        # Generate association rules
        rules = association_rules(
            frequent_itemsets,
            metric="confidence",
            min_threshold=self.min_confidence,
            num_itemsets=len(frequent_itemsets)
        )
        
        if rules.empty:
            logger.warning("No association rules found with current parameters")
            return rules
        
        # Add additional metrics
        rules['rule_length'] = rules['antecedents'].apply(len) + rules['consequents'].apply(len)
        rules['transaction_count'] = (rules['support'] * len(frequent_itemsets)).astype(int)
        
        # Sort by confidence and lift
        rules = rules.sort_values(['confidence', 'lift'], ascending=False)
        
        logger.info(f"Generated {len(rules)} association rules")
        logger.info(f"Average confidence: {rules['confidence'].mean():.3f}")
        logger.info(f"Average lift: {rules['lift'].mean():.3f}")
        
        self.association_rules_df = rules
        return rules
    
    def save_to_database(self, 
                        frequent_itemsets: pd.DataFrame, 
                        association_rules: pd.DataFrame,
                        training_stats: Dict) -> None:
        """
        Save mining results to database
        
        Args:
            frequent_itemsets: DataFrame with frequent itemsets
            association_rules: DataFrame with association rules
            training_stats: Training statistics
        """
        logger.info("Saving mining results to database...")
        
        # Create training log
        self.training_log = ModelTrainingLog.objects.create(
            model_type='apriori',
            model_version=self.model_version,
            min_support=self.min_support,
            min_confidence=self.min_confidence,
            max_itemsets=self.max_itemsets,
            total_transactions=training_stats['total_transactions'],
            total_products=training_stats['total_products'],
            date_range_start=training_stats['date_range_start'],
            date_range_end=training_stats['date_range_end'],
            frequent_itemsets_count=len(frequent_itemsets),
            association_rules_count=len(association_rules),
            recommendations_generated=0,  # Will be updated later
            training_duration_seconds=training_stats['training_duration'],
            status='completed'
        )
        
        # Save frequent itemsets
        self._save_frequent_itemsets(frequent_itemsets)
        
        # Save association rules
        self._save_association_rules(association_rules)
        
        logger.info(f"Saved {len(frequent_itemsets)} itemsets and {len(association_rules)} rules to database")
    
    def _save_frequent_itemsets(self, frequent_itemsets: pd.DataFrame) -> None:
        """Save frequent itemsets to database"""
        # Clear old itemsets for this model version
        FrequentItemset.objects.filter(model_version=self.model_version).delete()

        itemsets_to_create = []
        for _, row in frequent_itemsets.iterrows():
            itemset = FrequentItemset(
                support=float(row['support']),
                itemset_size=int(row['itemset_size']),
                transaction_count=int(row['transaction_count']),
                model_version=self.model_version
            )
            itemsets_to_create.append(itemset)

        # Bulk create itemsets
        created_itemsets = FrequentItemset.objects.bulk_create(itemsets_to_create)

        # Add products to itemsets (skip if products don't exist in database)
        for itemset_obj, (_, row) in zip(created_itemsets, frequent_itemsets.iterrows()):
            product_ids = list(row['itemsets'])
            try:
                products = Product.objects.filter(id__in=product_ids)
                if products.exists():
                    itemset_obj.products.set(products)
                else:
                    logger.warning(f"No products found for itemset {product_ids}")
            except Exception as e:
                logger.warning(f"Error setting products for itemset: {e}")
    
    def _save_association_rules(self, association_rules: pd.DataFrame) -> None:
        """Save association rules to database"""
        # Clear old rules for this model version
        AssociationRule.objects.filter(model_version=self.model_version).delete()

        rules_to_create = []
        for _, row in association_rules.iterrows():
            rule = AssociationRule(
                support=float(row['support']),
                confidence=float(row['confidence']),
                lift=float(row['lift']),
                conviction=float(row.get('conviction', 0)) if pd.notna(row.get('conviction')) else None,
                rule_length=int(row['rule_length']),
                transaction_count=int(row['transaction_count']),
                model_version=self.model_version
            )
            rules_to_create.append(rule)

        # Bulk create rules
        created_rules = AssociationRule.objects.bulk_create(rules_to_create)

        # Add antecedent and consequent products (skip if products don't exist)
        for rule_obj, (_, row) in zip(created_rules, association_rules.iterrows()):
            antecedent_ids = list(row['antecedents'])
            consequent_ids = list(row['consequents'])

            try:
                antecedent_products = Product.objects.filter(id__in=antecedent_ids)
                consequent_products = Product.objects.filter(id__in=consequent_ids)

                if antecedent_products.exists() and consequent_products.exists():
                    rule_obj.antecedent.set(antecedent_products)
                    rule_obj.consequent.set(consequent_products)
                else:
                    logger.warning(f"Products not found for rule: {antecedent_ids} -> {consequent_ids}")
            except Exception as e:
                logger.warning(f"Error setting products for rule: {e}")
    
    def train(self, data_source: str = 'processed') -> Dict:
        """
        Complete training pipeline
        
        Args:
            data_source: Source of transaction data
            
        Returns:
            Training results and statistics
        """
        start_time = timezone.now()
        logger.info(f"Starting association rule mining training - {self.model_version}")
        
        try:
            # Load data
            binary_matrix = self.load_transaction_data(data_source)
            
            # Find frequent itemsets
            frequent_itemsets = self.find_frequent_itemsets(binary_matrix)
            
            if frequent_itemsets.empty:
                raise ValueError("No frequent itemsets found. Try lowering min_support.")
            
            # Generate association rules
            association_rules = self.generate_association_rules(frequent_itemsets)
            
            if association_rules.empty:
                raise ValueError("No association rules found. Try lowering min_confidence.")
            
            # Calculate training statistics
            end_time = timezone.now()
            training_duration = (end_time - start_time).total_seconds()
            
            training_stats = {
                'total_transactions': len(binary_matrix),
                'total_products': len(binary_matrix.columns),
                'date_range_start': start_time,
                'date_range_end': end_time,
                'training_duration': training_duration,
                'frequent_itemsets_count': len(frequent_itemsets),
                'association_rules_count': len(association_rules)
            }
            
            # Save to database
            self.save_to_database(frequent_itemsets, association_rules, training_stats)
            
            logger.info(f"Training completed successfully in {training_duration:.2f} seconds")
            
            return {
                'status': 'success',
                'model_version': self.model_version,
                'training_stats': training_stats,
                'frequent_itemsets': frequent_itemsets,
                'association_rules': association_rules
            }
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            
            # Update training log with failure
            if self.training_log:
                self.training_log.status = 'failed'
                self.training_log.error_message = str(e)
                self.training_log.save()
            
            raise
