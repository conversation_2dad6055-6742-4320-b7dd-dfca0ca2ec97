"""
Model evaluation framework for cross-selling recommendations
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging
from sklearn.model_selection import train_test_split
from sklearn.metrics import precision_score, recall_score, f1_score
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from django.conf import settings

from .ml_engine import AssociationRuleMiner
from .recommendation_engine import CrossSellRecommendationEngine

logger = logging.getLogger(__name__)


class CrossSellModelEvaluator:
    """
    Comprehensive evaluation framework for cross-selling models
    """
    
    def __init__(self, test_size: float = 0.2, random_state: int = 42):
        """
        Initialize model evaluator
        
        Args:
            test_size: Proportion of data to use for testing
            random_state: Random seed for reproducibility
        """
        self.test_size = test_size
        self.random_state = random_state
        self.evaluation_results = {}
        
    def prepare_evaluation_data(self, binary_matrix: pd.DataFrame) -> <PERSON><PERSON>[pd.DataFrame, pd.DataFrame]:
        """
        Split data into training and testing sets
        
        Args:
            binary_matrix: Binary transaction matrix
            
        Returns:
            Tuple of (train_data, test_data)
        """
        logger.info(f"Splitting data: {len(binary_matrix)} transactions")
        
        # Split transactions
        train_data, test_data = train_test_split(
            binary_matrix, 
            test_size=self.test_size, 
            random_state=self.random_state
        )
        
        logger.info(f"Train set: {len(train_data)} transactions")
        logger.info(f"Test set: {len(test_data)} transactions")
        
        return train_data, test_data
    
    def evaluate_recommendation_accuracy(self, 
                                       model_version: str,
                                       test_data: pd.DataFrame,
                                       top_k: int = 5) -> Dict:
        """
        Evaluate recommendation accuracy using test data
        
        Args:
            model_version: Trained model version
            test_data: Test transaction data
            top_k: Number of top recommendations to evaluate
            
        Returns:
            Dictionary with evaluation metrics
        """
        logger.info(f"Evaluating recommendation accuracy for model {model_version}")
        
        engine = CrossSellRecommendationEngine(model_version=model_version)
        
        # Metrics storage
        precision_scores = []
        recall_scores = []
        hit_rates = []
        
        total_transactions = len(test_data)
        evaluated_transactions = 0
        
        for transaction_id, transaction in test_data.iterrows():
            # Get products in this transaction
            actual_products = set(transaction[transaction == 1].index.tolist())
            
            if len(actual_products) < 2:
                continue  # Skip single-item transactions
            
            # Split transaction: use first half to predict second half
            actual_list = list(actual_products)
            split_point = len(actual_list) // 2
            input_products = actual_list[:split_point]
            target_products = set(actual_list[split_point:])
            
            if not target_products:
                continue
            
            # Get recommendations
            try:
                recommendations = engine.get_product_recommendations(
                    input_products, 
                    max_recommendations=top_k
                )
                
                if not recommendations:
                    continue
                
                # Extract recommended product IDs
                recommended_products = set([rec['product_id'] for rec in recommendations])
                
                # Calculate metrics
                intersection = target_products.intersection(recommended_products)
                
                precision = len(intersection) / len(recommended_products) if recommended_products else 0
                recall = len(intersection) / len(target_products) if target_products else 0
                hit_rate = 1 if intersection else 0
                
                precision_scores.append(precision)
                recall_scores.append(recall)
                hit_rates.append(hit_rate)
                
                evaluated_transactions += 1
                
            except Exception as e:
                logger.warning(f"Error evaluating transaction {transaction_id}: {e}")
                continue
        
        # Calculate aggregate metrics
        if evaluated_transactions == 0:
            logger.warning("No transactions could be evaluated")
            return {
                'precision': 0.0,
                'recall': 0.0,
                'f1_score': 0.0,
                'hit_rate': 0.0,
                'coverage': 0.0,
                'evaluated_transactions': 0,
                'total_transactions': total_transactions
            }
        
        avg_precision = np.mean(precision_scores)
        avg_recall = np.mean(recall_scores)
        avg_f1 = 2 * (avg_precision * avg_recall) / (avg_precision + avg_recall) if (avg_precision + avg_recall) > 0 else 0
        avg_hit_rate = np.mean(hit_rates)
        coverage = evaluated_transactions / total_transactions
        
        results = {
            'precision': avg_precision,
            'recall': avg_recall,
            'f1_score': avg_f1,
            'hit_rate': avg_hit_rate,
            'coverage': coverage,
            'evaluated_transactions': evaluated_transactions,
            'total_transactions': total_transactions,
            'top_k': top_k
        }
        
        logger.info(f"Evaluation results: {results}")
        return results
    
    def evaluate_business_metrics(self, 
                                model_version: str,
                                test_data: pd.DataFrame) -> Dict:
        """
        Evaluate business-relevant metrics
        
        Args:
            model_version: Trained model version
            test_data: Test transaction data
            
        Returns:
            Dictionary with business metrics
        """
        logger.info("Evaluating business metrics")
        
        engine = CrossSellRecommendationEngine(model_version=model_version)
        
        # Metrics
        total_revenue_potential = 0
        cross_sell_opportunities = 0
        avg_basket_size_increase = []
        
        for transaction_id, transaction in test_data.iterrows():
            actual_products = transaction[transaction == 1].index.tolist()
            
            if len(actual_products) < 2:
                continue
            
            # Get recommendations for this basket
            try:
                recommendations = engine.get_product_recommendations(
                    actual_products[:1],  # Use first product as seed
                    max_recommendations=3
                )
                
                if recommendations:
                    cross_sell_opportunities += 1
                    
                    # Calculate potential revenue (using dummy prices)
                    rec_revenue = sum([rec.get('product_price', 10.0) for rec in recommendations])
                    total_revenue_potential += rec_revenue
                    
                    # Calculate basket size increase
                    basket_increase = len(recommendations)
                    avg_basket_size_increase.append(basket_increase)
                    
            except Exception as e:
                continue
        
        # Calculate business metrics
        avg_revenue_per_opportunity = total_revenue_potential / cross_sell_opportunities if cross_sell_opportunities > 0 else 0
        avg_basket_increase = np.mean(avg_basket_size_increase) if avg_basket_size_increase else 0
        cross_sell_rate = cross_sell_opportunities / len(test_data)
        
        business_metrics = {
            'total_revenue_potential': total_revenue_potential,
            'cross_sell_opportunities': cross_sell_opportunities,
            'avg_revenue_per_opportunity': avg_revenue_per_opportunity,
            'avg_basket_size_increase': avg_basket_increase,
            'cross_sell_rate': cross_sell_rate,
            'total_test_transactions': len(test_data)
        }
        
        logger.info(f"Business metrics: {business_metrics}")
        return business_metrics
    
    def evaluate_rule_quality(self, association_rules: pd.DataFrame) -> Dict:
        """
        Evaluate the quality of association rules
        
        Args:
            association_rules: DataFrame with association rules
            
        Returns:
            Dictionary with rule quality metrics
        """
        logger.info("Evaluating association rule quality")
        
        if association_rules.empty:
            return {
                'total_rules': 0,
                'avg_confidence': 0,
                'avg_lift': 0,
                'avg_support': 0,
                'high_confidence_rules': 0,
                'high_lift_rules': 0
            }
        
        # Basic statistics
        total_rules = len(association_rules)
        avg_confidence = association_rules['confidence'].mean()
        avg_lift = association_rules['lift'].mean()
        avg_support = association_rules['support'].mean()
        
        # Quality thresholds
        high_confidence_threshold = 0.5
        high_lift_threshold = 2.0
        
        high_confidence_rules = len(association_rules[association_rules['confidence'] >= high_confidence_threshold])
        high_lift_rules = len(association_rules[association_rules['lift'] >= high_lift_threshold])
        
        # Rule length distribution
        rule_lengths = association_rules['rule_length'].value_counts().to_dict()
        
        quality_metrics = {
            'total_rules': total_rules,
            'avg_confidence': avg_confidence,
            'avg_lift': avg_lift,
            'avg_support': avg_support,
            'high_confidence_rules': high_confidence_rules,
            'high_lift_rules': high_lift_rules,
            'high_confidence_rate': high_confidence_rules / total_rules,
            'high_lift_rate': high_lift_rules / total_rules,
            'rule_length_distribution': rule_lengths
        }
        
        logger.info(f"Rule quality metrics: {quality_metrics}")
        return quality_metrics
    
    def comprehensive_evaluation(self, 
                                binary_matrix: pd.DataFrame,
                                model_params: Dict) -> Dict:
        """
        Run comprehensive evaluation of the model
        
        Args:
            binary_matrix: Full binary transaction matrix
            model_params: Model training parameters
            
        Returns:
            Complete evaluation results
        """
        logger.info("Starting comprehensive model evaluation")
        
        # Split data
        train_data, test_data = self.prepare_evaluation_data(binary_matrix)
        
        # Train model on training data
        logger.info("Training model on training data")
        miner = AssociationRuleMiner(**model_params)
        
        # Save training data temporarily
        train_data_path = Path(settings.BASE_DIR) / 'data' / 'processed' / 'train_binary_matrix.csv'
        train_data.to_csv(train_data_path)
        
        # Train model
        training_results = miner.train(data_source=str(train_data_path))
        model_version = training_results['model_version']
        
        # Evaluate on test data
        logger.info("Evaluating on test data")
        
        # 1. Recommendation accuracy
        accuracy_metrics = self.evaluate_recommendation_accuracy(
            model_version, test_data, top_k=5
        )
        
        # 2. Business metrics
        business_metrics = self.evaluate_business_metrics(
            model_version, test_data
        )
        
        # 3. Rule quality
        rule_quality = self.evaluate_rule_quality(
            training_results['association_rules']
        )
        
        # Combine all results
        evaluation_results = {
            'model_version': model_version,
            'model_params': model_params,
            'data_split': {
                'train_size': len(train_data),
                'test_size': len(test_data),
                'test_ratio': self.test_size
            },
            'training_results': training_results['training_stats'],
            'accuracy_metrics': accuracy_metrics,
            'business_metrics': business_metrics,
            'rule_quality': rule_quality,
            'overall_score': self._calculate_overall_score(accuracy_metrics, business_metrics, rule_quality)
        }
        
        self.evaluation_results[model_version] = evaluation_results
        
        # Cleanup temporary file
        if train_data_path.exists():
            train_data_path.unlink()
        
        logger.info(f"Comprehensive evaluation completed for model {model_version}")
        return evaluation_results
    
    def _calculate_overall_score(self, accuracy_metrics: Dict, business_metrics: Dict, rule_quality: Dict) -> float:
        """Calculate overall model score"""
        # Weighted combination of different metrics
        accuracy_weight = 0.4
        business_weight = 0.4
        quality_weight = 0.2
        
        # Normalize metrics to 0-1 scale
        accuracy_score = (accuracy_metrics['precision'] + accuracy_metrics['recall'] + accuracy_metrics['hit_rate']) / 3
        business_score = min(business_metrics['cross_sell_rate'] * 2, 1.0)  # Cap at 1.0
        quality_score = min(rule_quality['high_confidence_rate'] + rule_quality['high_lift_rate'], 1.0)
        
        overall_score = (
            accuracy_score * accuracy_weight +
            business_score * business_weight +
            quality_score * quality_weight
        )
        
        return overall_score
    
    def save_evaluation_results(self, output_dir: str = None):
        """Save evaluation results to files"""
        if output_dir is None:
            output_dir = Path(settings.BASE_DIR) / 'data' / 'processed' / 'evaluations'
        else:
            output_dir = Path(output_dir)
        
        output_dir.mkdir(parents=True, exist_ok=True)
        
        for model_version, results in self.evaluation_results.items():
            # Save as JSON
            import json
            results_file = output_dir / f"evaluation_{model_version}.json"
            
            # Convert numpy types to native Python types for JSON serialization
            def convert_numpy(obj):
                if isinstance(obj, np.integer):
                    return int(obj)
                elif isinstance(obj, np.floating):
                    return float(obj)
                elif isinstance(obj, np.ndarray):
                    return obj.tolist()
                return obj
            
            # Deep convert the results
            json_results = json.loads(json.dumps(results, default=convert_numpy))
            
            with open(results_file, 'w') as f:
                json.dump(json_results, f, indent=2)
            
            logger.info(f"Saved evaluation results to {results_file}")
    
    def compare_models(self, model_versions: List[str]) -> pd.DataFrame:
        """Compare multiple model versions"""
        comparison_data = []
        
        for version in model_versions:
            if version in self.evaluation_results:
                results = self.evaluation_results[version]
                comparison_data.append({
                    'model_version': version,
                    'overall_score': results['overall_score'],
                    'precision': results['accuracy_metrics']['precision'],
                    'recall': results['accuracy_metrics']['recall'],
                    'f1_score': results['accuracy_metrics']['f1_score'],
                    'hit_rate': results['accuracy_metrics']['hit_rate'],
                    'cross_sell_rate': results['business_metrics']['cross_sell_rate'],
                    'avg_confidence': results['rule_quality']['avg_confidence'],
                    'avg_lift': results['rule_quality']['avg_lift'],
                    'total_rules': results['rule_quality']['total_rules']
                })
        
        comparison_df = pd.DataFrame(comparison_data)
        return comparison_df.sort_values('overall_score', ascending=False)
