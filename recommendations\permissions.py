"""
Custom permissions for Recommendations API
"""
from rest_framework import permissions


class CanTrainModels(permissions.BasePermission):
    """
    Permission for model training operations
    """

    def has_permission(self, request, view):
        return (
            request.user.is_authenticated and 
            (request.user.is_staff or request.user.has_perm('recommendations.train_models'))
        )


class CanViewModelData(permissions.BasePermission):
    """
    Permission for viewing model data and analytics
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        return (
            request.user.is_authenticated and 
            (request.user.is_staff or request.user.has_perm('recommendations.view_model_data'))
        )


class CanManageRecommendations(permissions.BasePermission):
    """
    Permission for managing recommendations
    """

    def has_permission(self, request, view):
        if request.method in permissions.SAFE_METHODS:
            return True
        
        return (
            request.user.is_authenticated and 
            (request.user.is_staff or request.user.has_perm('recommendations.change_recommendation'))
        )


class IsOwnerOrStaff(permissions.BasePermission):
    """
    Permission for customer-specific recommendations
    """

    def has_object_permission(self, request, view, obj):
        # Staff can access all objects
        if request.user.is_staff:
            return True
        
        # Customers can only access their own recommendations
        if hasattr(obj, 'customer') and hasattr(request.user, 'customer_profile'):
            return obj.customer == request.user.customer_profile
        
        return False
