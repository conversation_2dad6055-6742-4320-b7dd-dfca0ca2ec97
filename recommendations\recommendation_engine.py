"""
Recommendation Engine using Association Rules
"""
import pandas as pd
from typing import Dict, List, Tuple, Optional, Set
import logging
from django.db.models import Q
from django.utils import timezone
from datetime import timed<PERSON><PERSON>

from .models import AssociationRule, ProductRecommendation, CustomerRecommendation
from products.models import Product, Customer, Cart, OrderItem

logger = logging.getLogger(__name__)


class CrossSellRecommendationEngine:
    """
    Generate cross-selling recommendations using association rules
    """
    
    def __init__(self, model_version: str = None):
        """
        Initialize recommendation engine
        
        Args:
            model_version: Specific model version to use (latest if None)
        """
        self.model_version = model_version
        if not model_version:
            # Get latest model version
            latest_rule = AssociationRule.objects.filter(is_active=True).order_by('-created_at').first()
            self.model_version = latest_rule.model_version if latest_rule else None
        
        if not self.model_version:
            raise ValueError("No trained association rules found. Please train a model first.")
        
        logger.info(f"Initialized recommendation engine with model version: {self.model_version}")
    
    def get_product_recommendations(self, 
                                  product_ids: List[int], 
                                  max_recommendations: int = 10,
                                  min_confidence: float = None,
                                  min_lift: float = 1.0) -> List[Dict]:
        """
        Get product recommendations based on association rules
        
        Args:
            product_ids: List of product IDs in current basket
            max_recommendations: Maximum number of recommendations
            min_confidence: Minimum confidence threshold (uses model default if None)
            min_lift: Minimum lift threshold
            
        Returns:
            List of recommendation dictionaries
        """
        if not product_ids:
            return []
        
        logger.info(f"Getting recommendations for products: {product_ids}")
        
        # Get association rules where antecedent contains any of the input products
        rules_query = AssociationRule.objects.filter(
            model_version=self.model_version,
            is_active=True,
            lift__gte=min_lift
        )
        
        if min_confidence:
            rules_query = rules_query.filter(confidence__gte=min_confidence)
        
        # Find rules where antecedent products match input products
        matching_rules = []
        for rule in rules_query.prefetch_related('antecedent', 'consequent'):
            antecedent_ids = set(rule.antecedent.values_list('id', flat=True))
            input_set = set(product_ids)
            
            # Check if input products are a subset of or overlap with antecedent
            if input_set.intersection(antecedent_ids):
                matching_rules.append(rule)
        
        # Score and rank recommendations
        recommendations = self._score_recommendations(matching_rules, product_ids, max_recommendations)
        
        logger.info(f"Generated {len(recommendations)} recommendations")
        return recommendations
    
    def _score_recommendations(self, 
                             rules: List[AssociationRule], 
                             input_products: List[int],
                             max_recommendations: int) -> List[Dict]:
        """
        Score and rank recommendations from matching rules
        """
        product_scores = {}
        
        for rule in rules:
            antecedent_ids = set(rule.antecedent.values_list('id', flat=True))
            consequent_ids = set(rule.consequent.values_list('id', flat=True))
            input_set = set(input_products)
            
            # Skip if consequent products are already in input
            if consequent_ids.intersection(input_set):
                continue
            
            # Calculate overlap score (how many input products match antecedent)
            overlap = len(input_set.intersection(antecedent_ids))
            overlap_ratio = overlap / len(antecedent_ids) if antecedent_ids else 0
            
            # Calculate recommendation score
            # Combines confidence, lift, and overlap ratio
            score = (rule.confidence * 0.4 + 
                    (rule.lift / 10.0) * 0.3 + 
                    overlap_ratio * 0.3)
            
            # Add each consequent product with this score
            for product_id in consequent_ids:
                if product_id not in product_scores:
                    product_scores[product_id] = {
                        'product_id': product_id,
                        'score': 0,
                        'rules_count': 0,
                        'max_confidence': 0,
                        'max_lift': 0,
                        'supporting_rules': []
                    }
                
                # Aggregate scores (take maximum or average)
                current = product_scores[product_id]
                current['score'] = max(current['score'], score)
                current['rules_count'] += 1
                current['max_confidence'] = max(current['max_confidence'], rule.confidence)
                current['max_lift'] = max(current['max_lift'], rule.lift)
                current['supporting_rules'].append({
                    'rule_id': rule.id,
                    'confidence': rule.confidence,
                    'lift': rule.lift,
                    'support': rule.support
                })
        
        # Sort by score and limit results
        sorted_recommendations = sorted(
            product_scores.values(), 
            key=lambda x: x['score'], 
            reverse=True
        )[:max_recommendations]
        
        # Enrich with product information
        product_ids = [rec['product_id'] for rec in sorted_recommendations]
        products = Product.objects.filter(id__in=product_ids).select_related('category')
        product_dict = {p.id: p for p in products}
        
        for rec in sorted_recommendations:
            product = product_dict.get(rec['product_id'])
            if product:
                rec.update({
                    'product_name': product.name,
                    'product_price': float(product.effective_price),
                    'category_name': product.category.name if product.category else None,
                    'is_active': product.is_active,
                    'stock_available': product.is_in_stock
                })
        
        return sorted_recommendations
    
    def get_cart_recommendations(self, cart_id: int, max_recommendations: int = 5) -> List[Dict]:
        """
        Get recommendations for items in a shopping cart
        
        Args:
            cart_id: Cart ID
            max_recommendations: Maximum recommendations
            
        Returns:
            List of recommendations
        """
        try:
            cart = Cart.objects.get(id=cart_id)
            product_ids = list(cart.items.values_list('product_id', flat=True))
            return self.get_product_recommendations(product_ids, max_recommendations)
        except Cart.DoesNotExist:
            logger.warning(f"Cart {cart_id} not found")
            return []
    
    def get_customer_recommendations(self, 
                                   customer_id: int, 
                                   max_recommendations: int = 10,
                                   days_lookback: int = 30) -> List[Dict]:
        """
        Get personalized recommendations for a customer based on purchase history
        
        Args:
            customer_id: Customer ID
            max_recommendations: Maximum recommendations
            days_lookback: Days to look back for purchase history
            
        Returns:
            List of recommendations
        """
        try:
            customer = Customer.objects.get(id=customer_id)
            
            # Get recent purchase history
            cutoff_date = timezone.now() - timedelta(days=days_lookback)
            recent_orders = customer.orders.filter(
                created_at__gte=cutoff_date,
                status__in=['completed', 'delivered']
            )
            
            # Get frequently purchased products
            product_ids = list(
                OrderItem.objects.filter(
                    order__in=recent_orders
                ).values_list('product_id', flat=True).distinct()
            )
            
            if not product_ids:
                # Fallback to trending products if no history
                return self._get_trending_recommendations(max_recommendations)
            
            return self.get_product_recommendations(product_ids, max_recommendations)
            
        except Customer.DoesNotExist:
            logger.warning(f"Customer {customer_id} not found")
            return []
    
    def _get_trending_recommendations(self, max_recommendations: int) -> List[Dict]:
        """Get trending product recommendations as fallback"""
        trending_products = Product.objects.filter(
            is_active=True
        ).order_by('-popularity_score', '-rating_average')[:max_recommendations]
        
        recommendations = []
        for product in trending_products:
            recommendations.append({
                'product_id': product.id,
                'product_name': product.name,
                'product_price': float(product.effective_price),
                'category_name': product.category.name if product.category else None,
                'score': product.popularity_score,
                'recommendation_type': 'trending',
                'is_active': product.is_active,
                'stock_available': product.is_in_stock
            })
        
        return recommendations
    
    def generate_product_recommendations_batch(self, 
                                             force_regenerate: bool = False) -> int:
        """
        Generate and store product recommendations for all products
        
        Args:
            force_regenerate: Whether to regenerate existing recommendations
            
        Returns:
            Number of recommendations generated
        """
        logger.info("Starting batch recommendation generation...")
        
        if force_regenerate:
            # Clear existing recommendations for this model version
            ProductRecommendation.objects.filter(
                model_version=self.model_version
            ).delete()
        
        # Get all active products
        products = Product.objects.filter(is_active=True)
        recommendations_created = 0
        
        for product in products:
            # Skip if recommendations already exist
            if not force_regenerate and ProductRecommendation.objects.filter(
                source_product=product,
                model_version=self.model_version
            ).exists():
                continue
            
            # Get recommendations for this product
            recs = self.get_product_recommendations([product.id], max_recommendations=10)
            
            # Save recommendations
            for rec in recs:
                try:
                    recommended_product = Product.objects.get(id=rec['product_id'])
                    
                    ProductRecommendation.objects.create(
                        source_product=product,
                        recommended_product=recommended_product,
                        recommendation_type='cross_sell',
                        score=rec['score'],
                        algorithm_used='apriori',
                        model_version=self.model_version
                    )
                    recommendations_created += 1
                    
                except Product.DoesNotExist:
                    logger.warning(f"Product {rec['product_id']} not found")
                    continue
        
        logger.info(f"Generated {recommendations_created} product recommendations")
        return recommendations_created
    
    def get_bundle_suggestions(self, 
                             product_ids: List[int], 
                             max_bundles: int = 5) -> List[Dict]:
        """
        Get bundle suggestions for cross-selling
        
        Args:
            product_ids: Base products for bundle
            max_bundles: Maximum number of bundles
            
        Returns:
            List of bundle suggestions with pricing
        """
        recommendations = self.get_product_recommendations(
            product_ids, 
            max_recommendations=max_bundles * 2
        )
        
        bundles = []
        base_products = Product.objects.filter(id__in=product_ids)
        base_total = sum(p.effective_price for p in base_products)
        
        for i, rec in enumerate(recommendations[:max_bundles]):
            try:
                recommended_product = Product.objects.get(id=rec['product_id'])
                bundle_total = base_total + recommended_product.effective_price
                
                # Calculate bundle discount (e.g., 10% off)
                discount_percentage = 10
                discount_amount = bundle_total * (discount_percentage / 100)
                final_price = bundle_total - discount_amount
                
                bundles.append({
                    'bundle_id': f"bundle_{i+1}",
                    'base_products': [{'id': p.id, 'name': p.name, 'price': float(p.effective_price)} for p in base_products],
                    'recommended_product': {
                        'id': recommended_product.id,
                        'name': recommended_product.name,
                        'price': float(recommended_product.effective_price)
                    },
                    'original_total': float(bundle_total),
                    'discount_percentage': discount_percentage,
                    'discount_amount': float(discount_amount),
                    'final_price': float(final_price),
                    'savings': float(discount_amount),
                    'recommendation_score': rec['score']
                })
                
            except Product.DoesNotExist:
                continue
        
        return bundles
