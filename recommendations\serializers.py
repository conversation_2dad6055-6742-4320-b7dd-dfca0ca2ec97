"""
Serializers for Recommendations API
"""
from rest_framework import serializers
from django.contrib.auth.models import User
from .models import (
    AssociationRule, FrequentItemset, ProductRecommendation,
    CustomerRecommendation, RecommendationInteraction, ModelTrainingLog
)
from products.models import Product, Customer
from products.serializers import ProductListSerializer, CustomerSerializer


class FrequentItemsetSerializer(serializers.ModelSerializer):
    """Serializer for Frequent Itemsets"""
    products = ProductListSerializer(many=True, read_only=True)
    product_names = serializers.SerializerMethodField()
    
    class Meta:
        model = FrequentItemset
        fields = [
            'id', 'support', 'itemset_size', 'transaction_count',
            'model_version', 'products', 'product_names', 'created_at'
        ]
        read_only_fields = ['id', 'created_at', 'product_names']
    
    def get_product_names(self, obj):
        return [product.name for product in obj.products.all()]


class AssociationRuleSerializer(serializers.ModelSerializer):
    """Serializer for Association Rules"""
    antecedent = ProductListSerializer(many=True, read_only=True)
    consequent = ProductListSerializer(many=True, read_only=True)
    antecedent_names = serializers.SerializerMethodField()
    consequent_names = serializers.SerializerMethodField()
    rule_description = serializers.SerializerMethodField()
    
    class Meta:
        model = AssociationRule
        fields = [
            'id', 'support', 'confidence', 'lift', 'conviction',
            'rule_length', 'transaction_count', 'model_version',
            'antecedent', 'consequent', 'antecedent_names', 'consequent_names',
            'rule_description', 'is_active', 'created_at'
        ]
        read_only_fields = [
            'id', 'created_at', 'antecedent_names', 'consequent_names', 'rule_description'
        ]
    
    def get_antecedent_names(self, obj):
        return [product.name for product in obj.antecedent.all()]
    
    def get_consequent_names(self, obj):
        return [product.name for product in obj.consequent.all()]
    
    def get_rule_description(self, obj):
        antecedent_names = self.get_antecedent_names(obj)
        consequent_names = self.get_consequent_names(obj)
        return f"If {', '.join(antecedent_names)} then {', '.join(consequent_names)}"


class ProductRecommendationSerializer(serializers.ModelSerializer):
    """Serializer for Product Recommendations"""
    source_product = ProductListSerializer(read_only=True)
    recommended_product = ProductListSerializer(read_only=True)
    click_through_rate = serializers.ReadOnlyField()
    conversion_rate = serializers.ReadOnlyField()
    confidence = serializers.SerializerMethodField()
    support = serializers.SerializerMethodField()
    lift = serializers.SerializerMethodField()

    class Meta:
        model = ProductRecommendation
        fields = [
            'id', 'source_product', 'recommended_product', 'recommendation_type',
            'score', 'confidence', 'support', 'lift', 'algorithm_used',
            'model_version', 'view_count', 'click_count', 'conversion_count',
            'click_through_rate', 'conversion_rate', 'is_active', 'created_at'
        ]
        read_only_fields = [
            'id', 'view_count', 'click_count', 'conversion_count',
            'click_through_rate', 'conversion_rate', 'created_at'
        ]

    def get_confidence(self, obj):
        """Get confidence from associated rule"""
        return obj.association_rule.confidence if obj.association_rule else obj.score

    def get_support(self, obj):
        """Get support from associated rule"""
        return obj.association_rule.support if obj.association_rule else 0.0

    def get_lift(self, obj):
        """Get lift from associated rule"""
        return obj.association_rule.lift if obj.association_rule else 1.0


class CustomerRecommendationSerializer(serializers.ModelSerializer):
    """Serializer for Customer-specific Recommendations"""
    customer = CustomerSerializer(read_only=True)
    recommended_product = ProductListSerializer(read_only=True)
    
    class Meta:
        model = CustomerRecommendation
        fields = [
            'id', 'customer', 'recommended_product', 'recommendation_type',
            'score', 'reason', 'algorithm_used', 'model_version',
            'view_count', 'click_count', 'conversion_count',
            'is_active', 'expires_at', 'created_at'
        ]
        read_only_fields = [
            'id', 'view_count', 'click_count', 'conversion_count', 'created_at'
        ]


class RecommendationInteractionSerializer(serializers.ModelSerializer):
    """Serializer for Recommendation Interactions"""
    customer = CustomerSerializer(read_only=True)
    source_product = ProductListSerializer(read_only=True)
    recommended_product = ProductListSerializer(read_only=True)
    
    class Meta:
        model = RecommendationInteraction
        fields = [
            'id', 'customer', 'source_product', 'recommended_product',
            'interaction_type', 'session_key', 'ip_address', 'user_agent',
            'referrer', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ModelTrainingLogSerializer(serializers.ModelSerializer):
    """Serializer for Model Training Logs"""
    
    class Meta:
        model = ModelTrainingLog
        fields = [
            'id', 'model_version', 'algorithm', 'parameters', 'status',
            'training_duration', 'data_size', 'frequent_itemsets_count',
            'association_rules_count', 'error_message', 'created_at', 'completed_at'
        ]
        read_only_fields = ['id', 'created_at']


class RecommendationRequestSerializer(serializers.Serializer):
    """Serializer for recommendation requests"""
    product_ids = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        help_text="List of product IDs to get recommendations for"
    )
    customer_id = serializers.UUIDField(required=False, help_text="Customer ID for personalized recommendations")
    cart_items = serializers.ListField(
        child=serializers.DictField(),
        required=False,
        help_text="Current cart items for cart-based recommendations"
    )
    recommendation_type = serializers.ChoiceField(
        choices=['cross_sell', 'up_sell', 'similar', 'trending', 'personalized'],
        default='cross_sell',
        help_text="Type of recommendations to generate"
    )
    max_recommendations = serializers.IntegerField(
        default=5,
        min_value=1,
        max_value=20,
        help_text="Maximum number of recommendations to return"
    )
    min_confidence = serializers.FloatField(
        default=0.1,
        min_value=0.0,
        max_value=1.0,
        help_text="Minimum confidence threshold for recommendations"
    )
    min_lift = serializers.FloatField(
        default=1.0,
        min_value=0.0,
        help_text="Minimum lift threshold for recommendations"
    )
    exclude_products = serializers.ListField(
        child=serializers.UUIDField(),
        required=False,
        help_text="Product IDs to exclude from recommendations"
    )


class RecommendationResponseSerializer(serializers.Serializer):
    """Serializer for recommendation responses"""
    product_id = serializers.UUIDField()
    product_name = serializers.CharField()
    product_slug = serializers.CharField()
    product_price = serializers.DecimalField(max_digits=10, decimal_places=2)
    product_image = serializers.URLField(allow_null=True)
    score = serializers.FloatField()
    confidence = serializers.FloatField(allow_null=True)
    lift = serializers.FloatField(allow_null=True)
    reason = serializers.CharField()
    recommendation_type = serializers.CharField()
    algorithm_used = serializers.CharField()


class BundleRecommendationSerializer(serializers.Serializer):
    """Serializer for bundle recommendations"""
    bundle_id = serializers.CharField()
    products = ProductListSerializer(many=True)
    original_price = serializers.DecimalField(max_digits=10, decimal_places=2)
    bundle_price = serializers.DecimalField(max_digits=10, decimal_places=2)
    discount_amount = serializers.DecimalField(max_digits=10, decimal_places=2)
    discount_percentage = serializers.FloatField()
    confidence = serializers.FloatField()
    lift = serializers.FloatField()
    support = serializers.FloatField()
    bundle_name = serializers.CharField()
    bundle_description = serializers.CharField()


class RecommendationAnalyticsSerializer(serializers.Serializer):
    """Serializer for recommendation analytics"""
    total_recommendations = serializers.IntegerField()
    total_views = serializers.IntegerField()
    total_clicks = serializers.IntegerField()
    total_conversions = serializers.IntegerField()
    overall_ctr = serializers.FloatField()
    overall_conversion_rate = serializers.FloatField()
    top_performing_rules = AssociationRuleSerializer(many=True)
    recommendation_type_breakdown = serializers.DictField()
    algorithm_performance = serializers.DictField()


class TrainingRequestSerializer(serializers.Serializer):
    """Serializer for model training requests"""
    min_support = serializers.FloatField(
        default=0.01,
        min_value=0.001,
        max_value=0.1,
        help_text="Minimum support threshold"
    )
    min_confidence = serializers.FloatField(
        default=0.3,
        min_value=0.1,
        max_value=1.0,
        help_text="Minimum confidence threshold"
    )
    max_itemsets = serializers.IntegerField(
        default=500,
        min_value=100,
        max_value=5000,
        help_text="Maximum number of itemsets to generate"
    )
    force_retrain = serializers.BooleanField(
        default=False,
        help_text="Force retraining even if recent model exists"
    )
    data_source = serializers.ChoiceField(
        choices=['processed', 'sample'],
        default='processed',
        help_text="Data source for training"
    )


class TrainingStatusSerializer(serializers.Serializer):
    """Serializer for training status responses"""
    task_id = serializers.CharField()
    status = serializers.CharField()
    progress = serializers.IntegerField()
    current_step = serializers.CharField()
    model_version = serializers.CharField(allow_null=True)
    error_message = serializers.CharField(allow_null=True)
    started_at = serializers.DateTimeField()
    completed_at = serializers.DateTimeField(allow_null=True)
    duration = serializers.FloatField(allow_null=True)


class ModelPerformanceSerializer(serializers.Serializer):
    """Serializer for model performance metrics"""
    model_version = serializers.CharField()
    total_rules = serializers.IntegerField()
    total_itemsets = serializers.IntegerField()
    avg_confidence = serializers.FloatField()
    avg_lift = serializers.FloatField()
    avg_support = serializers.FloatField()
    training_duration = serializers.FloatField()
    data_size = serializers.IntegerField()
    last_trained = serializers.DateTimeField()
    is_active = serializers.BooleanField()


class RecommendationFeedbackSerializer(serializers.Serializer):
    """Serializer for recommendation feedback"""
    recommendation_id = serializers.UUIDField()
    feedback_type = serializers.ChoiceField(
        choices=['helpful', 'not_helpful', 'purchased', 'not_interested']
    )
    comment = serializers.CharField(required=False, max_length=500)
    rating = serializers.IntegerField(min_value=1, max_value=5, required=False)
