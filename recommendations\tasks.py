"""
Celery tasks for recommendation system
"""
from celery import shared_task
from django.conf import settings
import logging
import time

from .ml_engine import AssociationRuleMiner
from .recommendation_engine import CrossSellRecommendationEngine

logger = logging.getLogger(__name__)


@shared_task(bind=True)
def train_association_rules_task(self,
                                min_support=0.01,
                                min_confidence=0.5,
                                max_itemsets=1000,
                                data_source='processed',
                                model_version=None,
                                generate_recommendations=True):
    """
    Celery task to train association rules in the background
    
    Args:
        min_support: Minimum support threshold
        min_confidence: Minimum confidence threshold
        max_itemsets: Maximum number of itemsets
        data_source: Data source for training
        model_version: Model version identifier
        generate_recommendations: Whether to generate product recommendations
    
    Returns:
        dict: Training results
    """
    
    try:
        # Update task state
        self.update_state(state='PROGRESS', meta={'step': 'Initializing', 'progress': 10})
        
        # Initialize the miner
        miner = AssociationRuleMiner(
            min_support=min_support,
            min_confidence=min_confidence,
            max_itemsets=max_itemsets,
            model_version=model_version
        )
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'step': 'Loading data', 'progress': 20})
        
        # Train the model
        logger.info("Starting association rule training...")
        results = miner.train(data_source=data_source)
        
        # Update progress
        self.update_state(state='PROGRESS', meta={'step': 'Training completed', 'progress': 70})
        
        # Generate product recommendations if requested
        recommendations_count = 0
        if generate_recommendations:
            self.update_state(state='PROGRESS', meta={'step': 'Generating recommendations', 'progress': 80})
            
            engine = CrossSellRecommendationEngine(model_version=results['model_version'])
            recommendations_count = engine.generate_product_recommendations_batch(
                force_regenerate=True
            )
            
            # Update training log with recommendation count
            if miner.training_log:
                miner.training_log.recommendations_generated = recommendations_count
                miner.training_log.save()
        
        # Prepare final results
        final_results = {
            'status': 'completed',
            'model_version': results['model_version'],
            'training_stats': results['training_stats'],
            'recommendations_generated': recommendations_count,
            'frequent_itemsets_count': results['training_stats']['frequent_itemsets_count'],
            'association_rules_count': results['training_stats']['association_rules_count']
        }
        
        logger.info(f"Association rule training completed: {final_results}")
        return final_results
        
    except Exception as e:
        logger.error(f"Error in association rule training task: {e}")
        self.update_state(
            state='FAILURE',
            meta={'error': str(e), 'step': 'Error occurred'}
        )
        raise


@shared_task
def generate_product_recommendations_task(model_version=None, force_regenerate=False):
    """
    Task to generate product recommendations
    
    Args:
        model_version: Model version to use
        force_regenerate: Whether to regenerate existing recommendations
        
    Returns:
        dict: Generation results
    """
    try:
        engine = CrossSellRecommendationEngine(model_version=model_version)
        
        recommendations_count = engine.generate_product_recommendations_batch(
            force_regenerate=force_regenerate
        )
        
        return {
            'status': 'completed',
            'model_version': engine.model_version,
            'recommendations_generated': recommendations_count
        }
        
    except Exception as e:
        logger.error(f"Error in recommendation generation task: {e}")
        return {
            'status': 'failed',
            'error': str(e)
        }


@shared_task
def update_recommendation_performance_task():
    """
    Task to update recommendation performance metrics
    """
    try:
        from .models import ProductRecommendation, RecommendationInteraction
        from django.db.models import Count
        
        # Update click counts and conversion rates
        recommendations = ProductRecommendation.objects.filter(is_active=True)
        
        updated_count = 0
        for rec in recommendations:
            interactions = RecommendationInteraction.objects.filter(
                source_product=rec.source_product,
                recommended_product=rec.recommended_product
            )
            
            views = interactions.filter(interaction_type='view').count()
            clicks = interactions.filter(interaction_type='click').count()
            purchases = interactions.filter(interaction_type='purchase').count()
            
            if views != rec.view_count or clicks != rec.click_count or purchases != rec.conversion_count:
                rec.view_count = views
                rec.click_count = clicks
                rec.conversion_count = purchases
                rec.save()
                updated_count += 1
        
        return {
            'status': 'completed',
            'recommendations_updated': updated_count
        }
        
    except Exception as e:
        logger.error(f"Error in performance update task: {e}")
        return {
            'status': 'failed',
            'error': str(e)
        }


@shared_task
def cleanup_old_recommendations_task(days_old=30):
    """
    Task to cleanup old recommendation data
    
    Args:
        days_old: Number of days after which to delete old data
        
    Returns:
        dict: Cleanup results
    """
    try:
        from django.utils import timezone
        from datetime import timedelta
        from .models import ProductRecommendation, CustomerRecommendation, RecommendationInteraction
        
        cutoff_date = timezone.now() - timedelta(days=days_old)
        
        # Delete old customer recommendations
        old_customer_recs = CustomerRecommendation.objects.filter(
            created_at__lt=cutoff_date
        )
        customer_recs_deleted = old_customer_recs.count()
        old_customer_recs.delete()
        
        # Delete old interaction data
        old_interactions = RecommendationInteraction.objects.filter(
            created_at__lt=cutoff_date
        )
        interactions_deleted = old_interactions.count()
        old_interactions.delete()
        
        # Deactivate old product recommendations (don't delete, keep for analysis)
        old_product_recs = ProductRecommendation.objects.filter(
            created_at__lt=cutoff_date,
            is_active=True
        )
        product_recs_deactivated = old_product_recs.count()
        old_product_recs.update(is_active=False)
        
        return {
            'status': 'completed',
            'customer_recommendations_deleted': customer_recs_deleted,
            'interactions_deleted': interactions_deleted,
            'product_recommendations_deactivated': product_recs_deactivated,
            'cutoff_date': cutoff_date.isoformat()
        }
        
    except Exception as e:
        logger.error(f"Error in cleanup task: {e}")
        return {
            'status': 'failed',
            'error': str(e)
        }
