"""
Comprehensive training pipeline for cross-selling models
"""
import pandas as pd
import numpy as np
from typing import Dict, List, Optional
import logging
import time
import json
from pathlib import Path
from django.conf import settings
from django.utils import timezone

from .ml_engine import AssociationRuleMiner
from .model_evaluator import CrossSellModelEvaluator
from .hyperparameter_tuner import HyperparameterTuner
from .recommendation_engine import CrossSellRecommendationEngine
from .models import ModelTrainingLog

logger = logging.getLogger(__name__)


class CrossSellTrainingPipeline:
    """
    Complete training pipeline for cross-selling models
    """
    
    def __init__(self, 
                 auto_optimize: bool = True,
                 optimization_method: str = 'grid_search',
                 evaluation_enabled: bool = True):
        """
        Initialize training pipeline
        
        Args:
            auto_optimize: Whether to perform hyperparameter optimization
            optimization_method: Method for optimization ('grid_search', 'random_search', 'bayesian')
            evaluation_enabled: Whether to run comprehensive evaluation
        """
        self.auto_optimize = auto_optimize
        self.optimization_method = optimization_method
        self.evaluation_enabled = evaluation_enabled
        self.pipeline_results = {}
        
    def run_full_pipeline(self, 
                         data_source: str = 'processed',
                         baseline_params: Dict = None,
                         optimization_config: Dict = None) -> Dict:
        """
        Run the complete training pipeline
        
        Args:
            data_source: Source of training data
            baseline_params: Baseline model parameters
            optimization_config: Configuration for hyperparameter optimization
            
        Returns:
            Complete pipeline results
        """
        start_time = time.time()
        logger.info("Starting complete cross-selling model training pipeline")
        
        # Default parameters
        if baseline_params is None:
            baseline_params = {
                'min_support': 0.01,
                'min_confidence': 0.3,
                'max_itemsets': 500
            }
        
        if optimization_config is None:
            optimization_config = {
                'max_combinations': 20,
                'n_iterations': 15
            }
        
        try:
            # Step 1: Load and prepare data
            logger.info("Step 1: Loading and preparing data")
            binary_matrix = self._load_training_data(data_source)
            data_stats = self._analyze_data(binary_matrix)
            
            # Step 2: Train baseline model
            logger.info("Step 2: Training baseline model")
            baseline_results = self._train_baseline_model(binary_matrix, baseline_params)
            
            # Step 3: Hyperparameter optimization (if enabled)
            optimization_results = None
            if self.auto_optimize:
                logger.info("Step 3: Hyperparameter optimization")
                optimization_results = self._optimize_hyperparameters(
                    binary_matrix, optimization_config
                )
                best_params = optimization_results['best_params']
            else:
                best_params = baseline_params
            
            # Step 4: Train final model with best parameters
            logger.info("Step 4: Training final model with optimized parameters")
            final_model_results = self._train_final_model(binary_matrix, best_params)
            
            # Step 5: Comprehensive evaluation (if enabled)
            evaluation_results = None
            if self.evaluation_enabled:
                logger.info("Step 5: Comprehensive model evaluation")
                evaluation_results = self._evaluate_final_model(
                    binary_matrix, best_params
                )
            
            # Step 6: Generate production recommendations
            logger.info("Step 6: Generating production recommendations")
            recommendation_stats = self._generate_production_recommendations(
                final_model_results['model_version']
            )
            
            # Step 7: Create model comparison and analysis
            logger.info("Step 7: Creating model analysis")
            model_analysis = self._analyze_model_performance(
                baseline_results, final_model_results, evaluation_results
            )
            
            # Compile complete results
            pipeline_duration = time.time() - start_time
            
            complete_results = {
                'pipeline_config': {
                    'auto_optimize': self.auto_optimize,
                    'optimization_method': self.optimization_method,
                    'evaluation_enabled': self.evaluation_enabled,
                    'baseline_params': baseline_params,
                    'optimization_config': optimization_config
                },
                'data_statistics': data_stats,
                'baseline_model': baseline_results,
                'optimization_results': optimization_results,
                'final_model': final_model_results,
                'evaluation_results': evaluation_results,
                'recommendation_stats': recommendation_stats,
                'model_analysis': model_analysis,
                'pipeline_duration': pipeline_duration,
                'timestamp': timezone.now().isoformat(),
                'status': 'completed'
            }
            
            # Save results
            self._save_pipeline_results(complete_results)
            
            logger.info(f"Training pipeline completed successfully in {pipeline_duration:.2f} seconds")
            return complete_results
            
        except Exception as e:
            logger.error(f"Training pipeline failed: {e}")
            error_results = {
                'status': 'failed',
                'error': str(e),
                'pipeline_duration': time.time() - start_time,
                'timestamp': timezone.now().isoformat()
            }
            self._save_pipeline_results(error_results)
            raise
    
    def _load_training_data(self, data_source: str) -> pd.DataFrame:
        """Load training data"""
        if data_source == 'processed':
            data_path = Path(settings.BASE_DIR) / 'data' / 'processed' / 'filtered_binary_matrix.csv'
        else:
            data_path = Path(data_source)
        
        if not data_path.exists():
            raise FileNotFoundError(f"Training data not found at {data_path}")
        
        binary_matrix = pd.read_csv(data_path, index_col=0)
        logger.info(f"Loaded training data: {binary_matrix.shape}")
        return binary_matrix
    
    def _analyze_data(self, binary_matrix: pd.DataFrame) -> Dict:
        """Analyze training data characteristics"""
        stats = {
            'total_transactions': len(binary_matrix),
            'total_products': len(binary_matrix.columns),
            'sparsity': 1 - (binary_matrix.sum().sum() / (len(binary_matrix) * len(binary_matrix.columns))),
            'avg_basket_size': binary_matrix.sum(axis=1).mean(),
            'min_basket_size': binary_matrix.sum(axis=1).min(),
            'max_basket_size': binary_matrix.sum(axis=1).max(),
            'product_frequency_stats': {
                'mean': binary_matrix.sum(axis=0).mean(),
                'std': binary_matrix.sum(axis=0).std(),
                'min': binary_matrix.sum(axis=0).min(),
                'max': binary_matrix.sum(axis=0).max()
            }
        }
        
        logger.info(f"Data analysis: {stats}")
        return stats
    
    def _train_baseline_model(self, binary_matrix: pd.DataFrame, params: Dict) -> Dict:
        """Train baseline model"""
        logger.info(f"Training baseline model with params: {params}")
        
        # Save data temporarily
        temp_data_path = Path(settings.BASE_DIR) / 'data' / 'processed' / 'temp_baseline_data.csv'
        binary_matrix.to_csv(temp_data_path)
        
        try:
            # Train model
            miner = AssociationRuleMiner(**params, model_version=f"baseline_{int(time.time())}")
            results = miner.train(data_source=str(temp_data_path))
            
            # Add baseline flag
            results['is_baseline'] = True
            results['model_type'] = 'baseline'
            
            return results
            
        finally:
            # Cleanup
            if temp_data_path.exists():
                temp_data_path.unlink()
    
    def _optimize_hyperparameters(self, binary_matrix: pd.DataFrame, config: Dict) -> Dict:
        """Perform hyperparameter optimization"""
        tuner = HyperparameterTuner(evaluation_metric='overall_score')
        
        if self.optimization_method == 'grid_search':
            results = tuner.grid_search(
                binary_matrix, 
                max_combinations=config.get('max_combinations', 20)
            )
        elif self.optimization_method == 'random_search':
            results = tuner.random_search(
                binary_matrix,
                n_iterations=config.get('n_iterations', 15)
            )
        elif self.optimization_method == 'bayesian':
            results = tuner.bayesian_optimization(
                binary_matrix,
                n_iterations=config.get('n_iterations', 20)
            )
        else:
            raise ValueError(f"Unknown optimization method: {self.optimization_method}")
        
        # Save tuning results
        tuner.save_tuning_results()
        
        # Add parameter importance analysis
        results['parameter_importance'] = tuner.analyze_parameter_importance()
        
        return results
    
    def _train_final_model(self, binary_matrix: pd.DataFrame, params: Dict) -> Dict:
        """Train final model with optimized parameters"""
        logger.info(f"Training final model with optimized params: {params}")
        
        # Save data temporarily
        temp_data_path = Path(settings.BASE_DIR) / 'data' / 'processed' / 'temp_final_data.csv'
        binary_matrix.to_csv(temp_data_path)
        
        try:
            # Train model
            miner = AssociationRuleMiner(**params, model_version=f"optimized_{int(time.time())}")
            results = miner.train(data_source=str(temp_data_path))
            
            # Add optimization flags
            results['is_optimized'] = True
            results['model_type'] = 'optimized'
            results['optimized_params'] = params
            
            return results
            
        finally:
            # Cleanup
            if temp_data_path.exists():
                temp_data_path.unlink()
    
    def _evaluate_final_model(self, binary_matrix: pd.DataFrame, params: Dict) -> Dict:
        """Comprehensive evaluation of final model"""
        evaluator = CrossSellModelEvaluator(test_size=0.2)
        results = evaluator.comprehensive_evaluation(binary_matrix, params)
        
        # Save evaluation results
        evaluator.save_evaluation_results()
        
        return results
    
    def _generate_production_recommendations(self, model_version: str) -> Dict:
        """Generate production recommendations"""
        try:
            engine = CrossSellRecommendationEngine(model_version=model_version)
            recommendations_count = engine.generate_product_recommendations_batch(
                force_regenerate=True
            )
            
            return {
                'recommendations_generated': recommendations_count,
                'model_version': model_version,
                'generation_successful': True
            }
            
        except Exception as e:
            logger.error(f"Error generating production recommendations: {e}")
            return {
                'recommendations_generated': 0,
                'model_version': model_version,
                'generation_successful': False,
                'error': str(e)
            }
    
    def _analyze_model_performance(self, 
                                 baseline_results: Dict,
                                 final_results: Dict,
                                 evaluation_results: Dict = None) -> Dict:
        """Analyze and compare model performance"""
        analysis = {
            'baseline_vs_optimized': {
                'baseline_rules': baseline_results['training_stats']['association_rules_count'],
                'optimized_rules': final_results['training_stats']['association_rules_count'],
                'rules_improvement': final_results['training_stats']['association_rules_count'] - baseline_results['training_stats']['association_rules_count'],
                'baseline_itemsets': baseline_results['training_stats']['frequent_itemsets_count'],
                'optimized_itemsets': final_results['training_stats']['frequent_itemsets_count'],
                'itemsets_improvement': final_results['training_stats']['frequent_itemsets_count'] - baseline_results['training_stats']['frequent_itemsets_count']
            }
        }
        
        if evaluation_results:
            analysis['performance_metrics'] = {
                'overall_score': evaluation_results['overall_score'],
                'precision': evaluation_results['accuracy_metrics']['precision'],
                'recall': evaluation_results['accuracy_metrics']['recall'],
                'f1_score': evaluation_results['accuracy_metrics']['f1_score'],
                'cross_sell_rate': evaluation_results['business_metrics']['cross_sell_rate'],
                'avg_confidence': evaluation_results['rule_quality']['avg_confidence'],
                'avg_lift': evaluation_results['rule_quality']['avg_lift']
            }
        
        # Performance recommendations
        recommendations = []
        if analysis['baseline_vs_optimized']['rules_improvement'] > 0:
            recommendations.append("Hyperparameter optimization improved rule generation")
        else:
            recommendations.append("Consider different optimization strategy or parameters")
        
        if evaluation_results and evaluation_results['accuracy_metrics']['precision'] > 0.1:
            recommendations.append("Model shows good precision for recommendations")
        elif evaluation_results:
            recommendations.append("Consider increasing min_confidence to improve precision")
        
        analysis['recommendations'] = recommendations
        
        return analysis
    
    def _save_pipeline_results(self, results: Dict):
        """Save complete pipeline results"""
        # Save to file
        output_dir = Path(settings.BASE_DIR) / 'data' / 'processed' / 'training_pipelines'
        output_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = int(time.time())
        results_file = output_dir / f"pipeline_results_{timestamp}.json"
        
        # Convert numpy types for JSON serialization
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            return obj
        
        json_results = json.loads(json.dumps(results, default=convert_numpy))
        
        with open(results_file, 'w') as f:
            json.dump(json_results, f, indent=2)
        
        logger.info(f"Saved pipeline results to {results_file}")
        
        # Store in instance for later access
        self.pipeline_results = results
        
        return str(results_file)
    
    def get_model_recommendations(self) -> List[str]:
        """Get recommendations for model improvement"""
        if not self.pipeline_results:
            return ["Run training pipeline first"]
        
        recommendations = []
        
        # Check if optimization was beneficial
        if self.auto_optimize and 'optimization_results' in self.pipeline_results:
            opt_results = self.pipeline_results['optimization_results']
            if opt_results and opt_results['best_score'] > 0.5:
                recommendations.append("Hyperparameter optimization was successful")
            else:
                recommendations.append("Consider different optimization strategy")
        
        # Check evaluation results
        if self.evaluation_enabled and 'evaluation_results' in self.pipeline_results:
            eval_results = self.pipeline_results['evaluation_results']
            if eval_results:
                precision = eval_results['accuracy_metrics']['precision']
                recall = eval_results['accuracy_metrics']['recall']
                
                if precision < 0.1:
                    recommendations.append("Low precision - consider increasing min_confidence")
                if recall < 0.1:
                    recommendations.append("Low recall - consider decreasing min_support")
                if precision > 0.2 and recall > 0.2:
                    recommendations.append("Good balance of precision and recall achieved")
        
        # Data-specific recommendations
        if 'data_statistics' in self.pipeline_results:
            data_stats = self.pipeline_results['data_statistics']
            if data_stats['sparsity'] > 0.95:
                recommendations.append("Very sparse data - consider lowering min_support")
            if data_stats['avg_basket_size'] < 3:
                recommendations.append("Small basket sizes - focus on 2-item rules")
        
        return recommendations if recommendations else ["Model training completed successfully"]
