"""
URL configuration for Recommendations API
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

# Create router and register viewsets
router = DefaultRouter()
router.register(r'association-rules', views.AssociationRuleViewSet)
router.register(r'frequent-itemsets', views.FrequentItemsetViewSet)
router.register(r'product-recommendations', views.ProductRecommendationViewSet)
router.register(r'engine', views.RecommendationEngineViewSet, basename='recommendation-engine')
router.register(r'training', views.ModelTrainingViewSet, basename='model-training')

app_name = 'recommendations'

urlpatterns = [
    path('api/', include(router.urls)),
]
