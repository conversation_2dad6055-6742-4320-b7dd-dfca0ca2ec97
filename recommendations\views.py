"""
Recommendations API Views
"""
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Count, Avg, Sum
from django.shortcuts import get_object_or_404
from django.utils import timezone
from datetime import timedelta
import logging
import uuid

from .models import (
    AssociationRule, FrequentItemset, ProductRecommendation,
    CustomerRecommendation, RecommendationInteraction, ModelTrainingLog
)
from .serializers import (
    AssociationRuleSerializer, FrequentItemsetSerializer,
    ProductRecommendationSerializer, CustomerRecommendationSerializer,
    RecommendationInteractionSerializer, ModelTrainingLogSerializer,
    RecommendationRequestSerializer, RecommendationResponseSerializer,
    BundleRecommendationSerializer, RecommendationAnalyticsSerializer,
    TrainingRequestSerializer, TrainingStatusSerializer,
    ModelPerformanceSerializer, RecommendationFeedbackSerializer
)
from .recommendation_engine import CrossSellRecommendationEngine
from .tasks import train_association_rules_task, train_crosssell_model_task
from products.models import Product, Customer, Cart
from products.serializers import ProductListSerializer

logger = logging.getLogger(__name__)


class AssociationRuleViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for Association Rules
    Provides read-only access to association rules for analysis
    """
    queryset = AssociationRule.objects.filter(is_active=True)
    serializer_class = AssociationRuleSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['model_version', 'rule_length']
    ordering_fields = ['confidence', 'lift', 'support', 'created_at']
    ordering = ['-confidence', '-lift']

    @action(detail=False, methods=['get'])
    def top_rules(self, request):
        """Get top performing association rules"""
        limit = int(request.query_params.get('limit', 10))
        min_confidence = float(request.query_params.get('min_confidence', 0.3))
        min_lift = float(request.query_params.get('min_lift', 1.5))

        rules = self.get_queryset().filter(
            confidence__gte=min_confidence,
            lift__gte=min_lift
        ).order_by('-confidence', '-lift')[:limit]

        serializer = self.get_serializer(rules, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def by_product(self, request):
        """Get rules for a specific product"""
        product_id = request.query_params.get('product_id')
        if not product_id:
            return Response(
                {'error': 'product_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            product = Product.objects.get(id=product_id)
            rules = self.get_queryset().filter(
                Q(antecedent=product) | Q(consequent=product)
            ).distinct()

            serializer = self.get_serializer(rules, many=True)
            return Response(serializer.data)

        except Product.DoesNotExist:
            return Response(
                {'error': 'Product not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class FrequentItemsetViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for Frequent Itemsets
    """
    queryset = FrequentItemset.objects.all()
    serializer_class = FrequentItemsetSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['model_version', 'itemset_size']
    ordering_fields = ['support', 'itemset_size', 'created_at']
    ordering = ['-support']


class ProductRecommendationViewSet(viewsets.ReadOnlyModelViewSet):
    """
    ViewSet for Product Recommendations
    """
    queryset = ProductRecommendation.objects.filter(is_active=True)
    serializer_class = ProductRecommendationSerializer
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['recommendation_type', 'algorithm_used', 'model_version']
    ordering_fields = ['score', 'confidence', 'created_at']
    ordering = ['-score']

    @action(detail=False, methods=['get'])
    def for_product(self, request):
        """Get recommendations for a specific product"""
        product_id = request.query_params.get('product_id')
        if not product_id:
            return Response(
                {'error': 'product_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            product = Product.objects.get(id=product_id)
            recommendations = self.get_queryset().filter(
                source_product=product
            ).select_related('recommended_product')

            limit = int(request.query_params.get('limit', 5))
            recommendations = recommendations[:limit]

            serializer = self.get_serializer(recommendations, many=True)
            return Response(serializer.data)

        except Product.DoesNotExist:
            return Response(
                {'error': 'Product not found'},
                status=status.HTTP_404_NOT_FOUND
            )


class RecommendationEngineViewSet(viewsets.ViewSet):
    """
    Main ViewSet for Recommendation Engine API
    Provides endpoints for getting recommendations and managing the engine
    """
    permission_classes = [IsAuthenticatedOrReadOnly]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.engine = CrossSellRecommendationEngine()

    @action(detail=False, methods=['post'])
    def get_recommendations(self, request):
        """Get product recommendations based on various inputs"""
        serializer = RecommendationRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data
        recommendation_type = data.get('recommendation_type', 'cross_sell')
        max_recommendations = data.get('max_recommendations', 5)
        min_confidence = data.get('min_confidence', 0.1)
        min_lift = data.get('min_lift', 1.0)
        exclude_products = data.get('exclude_products', [])

        try:
            recommendations = []

            if recommendation_type == 'cross_sell' and data.get('product_ids'):
                recommendations = self.engine.get_product_recommendations(
                    product_ids=data['product_ids'],
                    max_recommendations=max_recommendations,
                    min_confidence=min_confidence,
                    min_lift=min_lift
                )

            elif recommendation_type == 'personalized' and data.get('customer_id'):
                recommendations = self.engine.get_customer_recommendations(
                    customer_id=data['customer_id'],
                    max_recommendations=max_recommendations
                )

            elif recommendation_type == 'cart' and data.get('cart_items'):
                # Extract product IDs from cart items
                product_ids = [item.get('product_id') for item in data['cart_items']]
                product_ids = [pid for pid in product_ids if pid]  # Filter None values

                if product_ids:
                    recommendations = self.engine.get_cart_recommendations(
                        cart_items=data['cart_items'],
                        max_recommendations=max_recommendations
                    )

            elif recommendation_type == 'trending':
                recommendations = self.engine._get_trending_recommendations(max_recommendations)

            # Filter out excluded products
            if exclude_products:
                recommendations = [
                    rec for rec in recommendations
                    if rec.get('product_id') not in exclude_products
                ]

            # Format response
            response_data = []
            for rec in recommendations:
                response_data.append({
                    'product_id': rec.get('product_id'),
                    'product_name': rec.get('product_name'),
                    'product_slug': rec.get('product_slug'),
                    'product_price': rec.get('product_price'),
                    'product_image': rec.get('product_image'),
                    'score': rec.get('score', 0.0),
                    'confidence': rec.get('confidence'),
                    'lift': rec.get('lift'),
                    'reason': rec.get('reason', 'Recommended for you'),
                    'recommendation_type': recommendation_type,
                    'algorithm_used': rec.get('algorithm_used', 'association_rules')
                })

            return Response({
                'recommendations': response_data,
                'total_count': len(response_data),
                'request_params': data
            })

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            return Response(
                {'error': 'Failed to generate recommendations'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def product_recommendations(self, request):
        """Get recommendations for specific products (GET endpoint)"""
        product_ids = request.query_params.getlist('product_ids')
        if not product_ids:
            return Response(
                {'error': 'product_ids parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        max_recommendations = int(request.query_params.get('max_recommendations', 5))
        min_confidence = float(request.query_params.get('min_confidence', 0.1))
        min_lift = float(request.query_params.get('min_lift', 1.0))

        try:
            recommendations = self.engine.get_product_recommendations(
                product_ids=product_ids,
                max_recommendations=max_recommendations,
                min_confidence=min_confidence,
                min_lift=min_lift
            )

            return Response({
                'recommendations': recommendations,
                'total_count': len(recommendations),
                'product_ids': product_ids
            })

        except Exception as e:
            logger.error(f"Error getting product recommendations: {e}")
            return Response(
                {'error': 'Failed to get recommendations'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def customer_recommendations(self, request):
        """Get personalized recommendations for a customer"""
        customer_id = request.query_params.get('customer_id')
        if not customer_id:
            return Response(
                {'error': 'customer_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        max_recommendations = int(request.query_params.get('max_recommendations', 5))
        days_lookback = int(request.query_params.get('days_lookback', 30))

        try:
            recommendations = self.engine.get_customer_recommendations(
                customer_id=customer_id,
                max_recommendations=max_recommendations,
                days_lookback=days_lookback
            )

            return Response({
                'recommendations': recommendations,
                'total_count': len(recommendations),
                'customer_id': customer_id
            })

        except Exception as e:
            logger.error(f"Error getting customer recommendations: {e}")
            return Response(
                {'error': 'Failed to get customer recommendations'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def cart_recommendations(self, request):
        """Get recommendations based on current cart contents"""
        cart_items = request.data.get('cart_items', [])
        if not cart_items:
            return Response(
                {'error': 'cart_items is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        max_recommendations = request.data.get('max_recommendations', 5)

        try:
            recommendations = self.engine.get_cart_recommendations(
                cart_items=cart_items,
                max_recommendations=max_recommendations
            )

            return Response({
                'recommendations': recommendations,
                'total_count': len(recommendations),
                'cart_items_count': len(cart_items)
            })

        except Exception as e:
            logger.error(f"Error getting cart recommendations: {e}")
            return Response(
                {'error': 'Failed to get cart recommendations'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def bundles(self, request):
        """Get bundle recommendations"""
        product_ids = request.query_params.getlist('product_ids')
        max_bundles = int(request.query_params.get('max_bundles', 3))
        min_confidence = float(request.query_params.get('min_confidence', 0.3))

        try:
            bundles = self.engine.get_bundle_recommendations(
                product_ids=product_ids if product_ids else None,
                max_bundles=max_bundles,
                min_confidence=min_confidence
            )

            return Response({
                'bundles': bundles,
                'total_count': len(bundles)
            })

        except Exception as e:
            logger.error(f"Error getting bundle recommendations: {e}")
            return Response(
                {'error': 'Failed to get bundle recommendations'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def track_interaction(self, request):
        """Track recommendation interaction"""
        source_product_id = request.data.get('source_product_id')
        recommended_product_id = request.data.get('recommended_product_id')
        interaction_type = request.data.get('interaction_type')

        if not all([source_product_id, recommended_product_id, interaction_type]):
            return Response(
                {'error': 'source_product_id, recommended_product_id, and interaction_type are required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            customer = None
            if request.user.is_authenticated:
                customer = getattr(request.user, 'customer_profile', None)

            interaction = RecommendationInteraction.objects.create(
                customer=customer,
                source_product_id=source_product_id,
                recommended_product_id=recommended_product_id,
                interaction_type=interaction_type,
                session_key=request.session.session_key or '',
                ip_address=request.META.get('REMOTE_ADDR', ''),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                referrer=request.META.get('HTTP_REFERER', '')
            )

            return Response({
                'message': 'Interaction tracked successfully',
                'interaction_id': interaction.id
            })

        except Exception as e:
            logger.error(f"Error tracking interaction: {e}")
            return Response(
                {'error': 'Failed to track interaction'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def analytics(self, request):
        """Get recommendation analytics"""
        days = int(request.query_params.get('days', 30))
        cutoff_date = timezone.now() - timedelta(days=days)

        try:
            # Get recommendation statistics
            total_recommendations = ProductRecommendation.objects.filter(is_active=True).count()

            # Get interaction statistics
            interactions = RecommendationInteraction.objects.filter(created_at__gte=cutoff_date)
            total_views = interactions.filter(interaction_type='view').count()
            total_clicks = interactions.filter(interaction_type='click').count()
            total_conversions = interactions.filter(interaction_type='purchase').count()

            # Calculate rates
            overall_ctr = (total_clicks / total_views) if total_views > 0 else 0
            overall_conversion_rate = (total_conversions / total_clicks) if total_clicks > 0 else 0

            # Get top performing rules
            top_rules = AssociationRule.objects.filter(
                is_active=True
            ).order_by('-confidence', '-lift')[:10]

            # Recommendation type breakdown
            type_breakdown = ProductRecommendation.objects.filter(
                is_active=True
            ).values('recommendation_type').annotate(
                count=Count('id')
            )

            return Response({
                'total_recommendations': total_recommendations,
                'total_views': total_views,
                'total_clicks': total_clicks,
                'total_conversions': total_conversions,
                'overall_ctr': overall_ctr,
                'overall_conversion_rate': overall_conversion_rate,
                'top_performing_rules': AssociationRuleSerializer(top_rules, many=True).data,
                'recommendation_type_breakdown': {item['recommendation_type']: item['count'] for item in type_breakdown},
                'period_days': days
            })

        except Exception as e:
            logger.error(f"Error getting analytics: {e}")
            return Response(
                {'error': 'Failed to get analytics'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ModelTrainingViewSet(viewsets.ViewSet):
    """
    ViewSet for Model Training and Management
    """
    permission_classes = [IsAuthenticated]

    @action(detail=False, methods=['post'])
    def train_model(self, request):
        """Start model training"""
        serializer = TrainingRequestSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        data = serializer.validated_data

        try:
            # Start background training task
            task = train_crosssell_model_task.delay(
                data_source=data.get('data_source', 'processed'),
                baseline_params={
                    'min_support': data.get('min_support', 0.01),
                    'min_confidence': data.get('min_confidence', 0.3),
                    'max_itemsets': data.get('max_itemsets', 500)
                },
                auto_optimize=True,
                optimization_method='random_search',
                evaluation_enabled=True
            )

            return Response({
                'message': 'Model training started',
                'task_id': task.id,
                'status': 'started',
                'parameters': data
            })

        except Exception as e:
            logger.error(f"Error starting model training: {e}")
            return Response(
                {'error': 'Failed to start model training'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def training_status(self, request):
        """Get training status"""
        task_id = request.query_params.get('task_id')
        if not task_id:
            return Response(
                {'error': 'task_id parameter is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from celery.result import AsyncResult
            result = AsyncResult(task_id)

            response_data = {
                'task_id': task_id,
                'status': result.status,
                'progress': 0,
                'current_step': 'Unknown',
                'model_version': None,
                'error_message': None,
                'started_at': None,
                'completed_at': None,
                'duration': None
            }

            if result.status == 'PENDING':
                response_data['current_step'] = 'Waiting to start'
            elif result.status == 'PROGRESS':
                if result.info:
                    response_data['progress'] = result.info.get('progress', 0)
                    response_data['current_step'] = result.info.get('step', 'Processing')
            elif result.status == 'SUCCESS':
                response_data['progress'] = 100
                response_data['current_step'] = 'Completed'
                if result.result:
                    response_data['model_version'] = result.result.get('summary', {}).get('final_model_version')
                    response_data['duration'] = result.result.get('pipeline_results', {}).get('pipeline_duration')
            elif result.status == 'FAILURE':
                response_data['current_step'] = 'Failed'
                response_data['error_message'] = str(result.info) if result.info else 'Unknown error'

            return Response(response_data)

        except Exception as e:
            logger.error(f"Error getting training status: {e}")
            return Response(
                {'error': 'Failed to get training status'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['get'])
    def training_history(self, request):
        """Get model training history"""
        logs = ModelTrainingLog.objects.all().order_by('-created_at')

        page = self.paginate_queryset(logs)
        if page is not None:
            serializer = ModelTrainingLogSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = ModelTrainingLogSerializer(logs, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=['get'])
    def model_performance(self, request):
        """Get current model performance metrics"""
        try:
            # Get latest model
            latest_log = ModelTrainingLog.objects.filter(
                status='completed'
            ).order_by('-completed_at').first()

            if not latest_log:
                return Response(
                    {'error': 'No trained models found'},
                    status=status.HTTP_404_NOT_FOUND
                )

            # Get model statistics
            model_version = latest_log.model_version
            total_rules = AssociationRule.objects.filter(
                model_version=model_version,
                is_active=True
            ).count()

            total_itemsets = FrequentItemset.objects.filter(
                model_version=model_version
            ).count()

            # Calculate average metrics
            rules = AssociationRule.objects.filter(
                model_version=model_version,
                is_active=True
            )

            avg_confidence = rules.aggregate(avg=Avg('confidence'))['avg'] or 0
            avg_lift = rules.aggregate(avg=Avg('lift'))['avg'] or 0
            avg_support = rules.aggregate(avg=Avg('support'))['avg'] or 0

            return Response({
                'model_version': model_version,
                'total_rules': total_rules,
                'total_itemsets': total_itemsets,
                'avg_confidence': avg_confidence,
                'avg_lift': avg_lift,
                'avg_support': avg_support,
                'training_duration': latest_log.training_duration,
                'data_size': latest_log.data_size,
                'last_trained': latest_log.completed_at,
                'is_active': True
            })

        except Exception as e:
            logger.error(f"Error getting model performance: {e}")
            return Response(
                {'error': 'Failed to get model performance'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'])
    def retrain_model(self, request):
        """Retrain model with new data"""
        force_retrain = request.data.get('force_retrain', False)

        # Check if recent training exists
        if not force_retrain:
            recent_training = ModelTrainingLog.objects.filter(
                status='completed',
                created_at__gte=timezone.now() - timedelta(days=1)
            ).first()

            if recent_training:
                return Response({
                    'message': 'Recent training found. Use force_retrain=true to override.',
                    'last_training': recent_training.created_at,
                    'model_version': recent_training.model_version
                })

        # Start retraining
        try:
            task = train_crosssell_model_task.delay(
                data_source='processed',
                auto_optimize=True,
                optimization_method='random_search',
                evaluation_enabled=True
            )

            return Response({
                'message': 'Model retraining started',
                'task_id': task.id,
                'status': 'started'
            })

        except Exception as e:
            logger.error(f"Error starting model retraining: {e}")
            return Response(
                {'error': 'Failed to start model retraining'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
