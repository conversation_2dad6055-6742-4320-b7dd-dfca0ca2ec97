#!/usr/bin/env python
"""
Development setup script to ensure proper environment configuration
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """Setup development environment"""
    project_root = Path(__file__).parent
    venv_path = project_root / "venv"
    
    print("🔧 Setting up development environment...")
    
    # Check if virtual environment exists
    if not venv_path.exists():
        print("❌ Virtual environment not found. Please run: python -m venv venv")
        return False
    
    # Check if Django is installed
    try:
        import django
        print(f"✅ Django {django.get_version()} is installed")
    except ImportError:
        print("❌ Django not found. Please install requirements: pip install -r requirements.txt")
        return False
    
    # Set Django settings module
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'crosssell_project.settings')
    
    # Setup Django
    try:
        django.setup()
        print("✅ Django setup completed")
    except Exception as e:
        print(f"❌ Django setup failed: {e}")
        return False
    
    # Check database connection (optional)
    try:
        from django.db import connection
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
        print("✅ Database connection successful")
    except Exception as e:
        print(f"⚠️  Database connection failed (this is OK for development): {e}")
    
    # Check if migrations exist
    migrations_dir = project_root / "products" / "migrations"
    if migrations_dir.exists() and any(migrations_dir.glob("*.py")):
        print("✅ Migrations found")
    else:
        print("⚠️  No migrations found. Run: python manage.py makemigrations")
    
    print("\n🎉 Development environment setup complete!")
    print("\nNext steps:")
    print("1. Configure your IDE to use: ./venv/Scripts/python.exe")
    print("2. Set DJANGO_SETTINGS_MODULE=crosssell_project.settings")
    print("3. Run migrations: python manage.py migrate")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
