#!/usr/bin/env python
"""
Simple API test script without Django setup
"""
import requests
import json
import time

def test_api_endpoints():
    """Test various API endpoints"""
    base_url = 'http://127.0.0.1:8000'
    
    print("🚀 Testing Cross-Selling API Endpoints")
    print("=" * 50)
    
    # Test 1: Check if server is running
    print("🔍 Testing Server Connection...")
    try:
        response = requests.get(f'{base_url}/api/products/', timeout=10)
        print(f"✅ Server is running - Status: {response.status_code}")
        if response.status_code != 200:
            print(f"   Response: {response.text[:200]}...")
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running. Please start with: python manage.py runserver")
        return False
    except Exception as e:
        print(f"❌ Error connecting to server: {e}")
        return False
    
    # Test 2: Products API
    print("\n📦 Testing Products API")
    endpoints = [
        ('/api/products/', 'Products List'),
        ('/api/products/featured/', 'Featured Products'),
        ('/api/products/trending/', 'Trending Products'),
        ('/api/products/search/?q=phone', 'Product Search'),
    ]
    
    for endpoint, name in endpoints:
        try:
            response = requests.get(f'{base_url}{endpoint}', timeout=5)
            print(f"   {name}: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                count = data.get('count', len(data.get('results', data)))
                print(f"     Found {count} items")
            elif response.status_code != 200:
                print(f"     Error: {response.text[:100]}...")
        except Exception as e:
            print(f"   {name}: Error - {e}")
    
    # Test 3: Categories API
    print("\n📂 Testing Categories API")
    try:
        response = requests.get(f'{base_url}/api/categories/', timeout=5)
        print(f"   Categories List: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            count = data.get('count', len(data.get('results', data)))
            print(f"     Found {count} categories")
        elif response.status_code != 200:
            print(f"     Error: {response.text[:100]}...")
    except Exception as e:
        print(f"   Categories: Error - {e}")
    
    # Test 4: Recommendations API
    print("\n🎯 Testing Recommendations API")
    rec_endpoints = [
        ('/recommendations/api/association-rules/', 'Association Rules'),
        ('/recommendations/api/frequent-itemsets/', 'Frequent Itemsets'),
        ('/recommendations/api/product-recommendations/', 'Product Recommendations'),
        ('/recommendations/api/engine/product_recommendations/?product_ids=1,2', 'Engine Recommendations'),
    ]
    
    for endpoint, name in rec_endpoints:
        try:
            response = requests.get(f'{base_url}{endpoint}', timeout=5)
            print(f"   {name}: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                count = data.get('count', len(data.get('results', data.get('recommendations', data))))
                print(f"     Found {count} items")
            elif response.status_code != 200:
                print(f"     Error: {response.text[:100]}...")
        except Exception as e:
            print(f"   {name}: Error - {e}")
    
    # Test 5: Authentication endpoints
    print("\n🔐 Testing Authentication")
    try:
        # Test token endpoint
        response = requests.post(f'{base_url}/api/auth/token/', {
            'username': 'testuser',
            'password': 'testpass123'
        }, timeout=5)
        print(f"   Token Auth: {response.status_code}")
        if response.status_code == 200:
            token_data = response.json()
            print(f"     Token received: {token_data.get('token', 'N/A')[:10]}...")
        elif response.status_code != 200:
            print(f"     Error: {response.text[:100]}...")
    except Exception as e:
        print(f"   Authentication: Error - {e}")
    
    # Test 6: Browsable API
    print("\n📚 Testing Browsable API")
    try:
        response = requests.get(f'{base_url}/api/', timeout=5)
        print(f"   Browsable API Root: {response.status_code}")
        if response.status_code == 200:
            print("     ✅ Browsable API is accessible")
    except Exception as e:
        print(f"   Browsable API: Error - {e}")
    
    # Test 7: Admin interface
    print("\n⚙️ Testing Admin Interface")
    try:
        response = requests.get(f'{base_url}/admin/', timeout=5)
        print(f"   Admin Interface: {response.status_code}")
        if response.status_code in [200, 302]:
            print("     ✅ Admin interface is accessible")
    except Exception as e:
        print(f"   Admin Interface: Error - {e}")
    
    print("\n" + "=" * 50)
    print("✅ API Testing Complete!")
    print("\n📋 Summary:")
    print("   - If you see 500 errors, check Django logs for details")
    print("   - If you see 403 errors, check authentication/permissions")
    print("   - If you see 404 errors, check URL configuration")
    print("   - All 200 responses indicate working endpoints!")
    
    return True

def test_post_endpoints():
    """Test POST endpoints that don't require authentication"""
    base_url = 'http://127.0.0.1:8000'
    
    print("\n🔄 Testing POST Endpoints")
    print("-" * 30)
    
    # Test recommendation request
    try:
        rec_data = {
            "product_ids": ["1", "2"],
            "recommendation_type": "cross_sell",
            "max_recommendations": 5
        }
        response = requests.post(
            f'{base_url}/recommendations/api/engine/get_recommendations/',
            json=rec_data,
            timeout=5
        )
        print(f"   Recommendation Request: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"     Recommendations: {len(data.get('recommendations', []))}")
    except Exception as e:
        print(f"   Recommendation Request: Error - {e}")

if __name__ == '__main__':
    print("🧪 Starting API Tests...")
    print("Make sure Django server is running: python manage.py runserver")
    print()
    
    # Wait a moment for server to be ready
    time.sleep(1)
    
    # Run tests
    test_api_endpoints()
    test_post_endpoints()
    
    print("\n🎉 Testing completed!")
    print("Visit http://127.0.0.1:8000/api/ to explore the browsable API")
