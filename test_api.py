#!/usr/bin/env python
"""
Test script for Cross-Selling API
"""
import os
import sys
import django
import requests
import json

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'crosssell_project.settings')
django.setup()

from django.contrib.auth.models import User
from rest_framework.authtoken.models import Token

def test_api_endpoints():
    """Test various API endpoints"""
    base_url = 'http://127.0.0.1:8000'
    
    print("🚀 Testing Cross-Selling API Endpoints")
    print("=" * 50)
    
    # Test 1: Check if server is running
    try:
        response = requests.get(f'{base_url}/api/products/', timeout=5)
        print(f"✅ Server is running - Status: {response.status_code}")
    except requests.exceptions.ConnectionError:
        print("❌ Server is not running. Please start with: python manage.py runserver")
        return False
    except Exception as e:
        print(f"❌ Error connecting to server: {e}")
        return False
    
    # Test 2: Products API
    print("\n📦 Testing Products API")
    try:
        response = requests.get(f'{base_url}/api/products/')
        print(f"   Products List: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {data.get('count', 0)} products")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 3: Categories API
    print("\n📂 Testing Categories API")
    try:
        response = requests.get(f'{base_url}/api/categories/')
        print(f"   Categories List: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {data.get('count', 0)} categories")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 4: Recommendations API
    print("\n🎯 Testing Recommendations API")
    try:
        response = requests.get(f'{base_url}/recommendations/api/association-rules/')
        print(f"   Association Rules: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"   Found {data.get('count', 0)} association rules")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 5: Create test user and get token
    print("\n👤 Testing Authentication")
    try:
        # Create test user if doesn't exist
        user, created = User.objects.get_or_create(
            username='testuser',
            defaults={
                'email': '<EMAIL>',
                'first_name': 'Test',
                'last_name': 'User'
            }
        )
        if created:
            user.set_password('testpass123')
            user.save()
            print("   Created test user")
        
        # Get or create token
        token, created = Token.objects.get_or_create(user=user)
        print(f"   Token: {token.key[:10]}...")
        
        # Test authenticated endpoint
        headers = {'Authorization': f'Token {token.key}'}
        response = requests.get(f'{base_url}/api/customers/profile/', headers=headers)
        print(f"   Profile Access: {response.status_code}")
        
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test 6: API Documentation
    print("\n📚 Testing API Documentation")
    try:
        response = requests.get(f'{base_url}/api/')
        print(f"   Browsable API: {response.status_code}")
    except Exception as e:
        print(f"   Error: {e}")
    
    print("\n" + "=" * 50)
    print("✅ API Testing Complete!")
    return True

def create_sample_data():
    """Create some sample data for testing"""
    print("\n🔧 Creating Sample Data")
    print("-" * 30)
    
    try:
        from products.models import Category, Product
        
        # Create sample category
        category, created = Category.objects.get_or_create(
            name='Electronics',
            defaults={
                'slug': 'electronics',
                'description': 'Electronic products and gadgets'
            }
        )
        if created:
            print("   Created Electronics category")
        
        # Create sample products
        products_data = [
            {
                'name': 'Smartphone',
                'slug': 'smartphone',
                'description': 'Latest smartphone with advanced features',
                'price': 699.99,
                'sku': 'PHONE001',
                'category': category
            },
            {
                'name': 'Laptop',
                'slug': 'laptop',
                'description': 'High-performance laptop for work and gaming',
                'price': 1299.99,
                'sku': 'LAPTOP001',
                'category': category
            },
            {
                'name': 'Headphones',
                'slug': 'headphones',
                'description': 'Wireless noise-canceling headphones',
                'price': 199.99,
                'sku': 'AUDIO001',
                'category': category
            }
        ]
        
        for product_data in products_data:
            product, created = Product.objects.get_or_create(
                sku=product_data['sku'],
                defaults=product_data
            )
            if created:
                print(f"   Created product: {product.name}")
        
        print("   Sample data creation complete!")
        
    except Exception as e:
        print(f"   Error creating sample data: {e}")

if __name__ == '__main__':
    # Create sample data first
    create_sample_data()
    
    # Test API endpoints
    test_api_endpoints()
