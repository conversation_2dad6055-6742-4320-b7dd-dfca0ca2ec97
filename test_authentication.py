#!/usr/bin/env python
"""
Comprehensive authentication testing
"""
import requests
import json

def test_authentication_flow():
    """Test complete authentication flow"""
    base_url = 'http://127.0.0.1:8000'
    
    print("🔐 Testing Authentication Flow")
    print("=" * 40)
    
    # Step 1: Create user and get token
    print("1️⃣ Testing Token Creation...")
    
    # Test token endpoint
    token_data = {
        'username': 'testuser',
        'password': 'testpass123'
    }
    
    response = requests.post(f'{base_url}/api/auth/token/', data=token_data)
    print(f"   Token Request: {response.status_code}")
    
    if response.status_code == 200:
        token_info = response.json()
        token = token_info.get('token')
        print(f"   ✅ Token received: {token[:10]}...")
        
        # Step 2: Test authenticated endpoints
        print("\n2️⃣ Testing Authenticated Endpoints...")
        headers = {'Authorization': f'Token {token}'}
        
        # Test customer profile
        response = requests.get(f'{base_url}/api/customers/profile/', headers=headers)
        print(f"   Customer Profile: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Profile access successful")
        
        # Test cart access
        response = requests.get(f'{base_url}/api/cart/current/', headers=headers)
        print(f"   Cart Access: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Cart access successful")
        
        # Test orders access
        response = requests.get(f'{base_url}/api/orders/', headers=headers)
        print(f"   Orders Access: {response.status_code}")
        if response.status_code == 200:
            print("   ✅ Orders access successful")
        
        # Step 3: Test cart operations
        print("\n3️⃣ Testing Cart Operations...")
        
        # Add item to cart
        cart_data = {
            'product_id': '1',  # Assuming we have a product with ID 1
            'quantity': 2
        }
        response = requests.post(f'{base_url}/api/cart/add_item/', json=cart_data, headers=headers)
        print(f"   Add to Cart: {response.status_code}")
        
        # Get cart contents
        response = requests.get(f'{base_url}/api/cart/current/', headers=headers)
        print(f"   Get Cart: {response.status_code}")
        if response.status_code == 200:
            cart_data = response.json()
            print(f"   Cart items: {len(cart_data.get('items', []))}")
        
        return token
        
    else:
        print(f"   ❌ Token creation failed: {response.text[:100]}")
        return None

def test_unauthenticated_access():
    """Test access without authentication"""
    base_url = 'http://127.0.0.1:8000'
    
    print("\n4️⃣ Testing Unauthenticated Access...")
    
    # These should work without auth
    public_endpoints = [
        '/api/products/',
        '/api/categories/',
        '/recommendations/api/association-rules/',
    ]
    
    for endpoint in public_endpoints:
        response = requests.get(f'{base_url}{endpoint}')
        print(f"   {endpoint}: {response.status_code}")
    
    # These should require auth
    protected_endpoints = [
        '/api/customers/profile/',
        '/api/cart/current/',
        '/api/orders/',
    ]
    
    print("\n   Testing protected endpoints without auth:")
    for endpoint in protected_endpoints:
        response = requests.get(f'{base_url}{endpoint}')
        print(f"   {endpoint}: {response.status_code} (should be 401 or 403)")

def test_invalid_token():
    """Test with invalid token"""
    base_url = 'http://127.0.0.1:8000'
    
    print("\n5️⃣ Testing Invalid Token...")
    
    headers = {'Authorization': 'Token invalid-token-12345'}
    response = requests.get(f'{base_url}/api/customers/profile/', headers=headers)
    print(f"   Invalid Token Access: {response.status_code} (should be 401)")

if __name__ == '__main__':
    print("🧪 Authentication Testing Suite")
    print("Make sure Django server is running!")
    print()
    
    # Run tests
    token = test_authentication_flow()
    test_unauthenticated_access()
    test_invalid_token()
    
    print("\n" + "=" * 40)
    if token:
        print("✅ Authentication testing completed successfully!")
        print(f"💡 Use this token for API testing: {token}")
    else:
        print("❌ Authentication testing failed!")
    
    print("\n📚 Next steps:")
    print("   - Use the token in Authorization header: 'Token <your-token>'")
    print("   - Test protected endpoints with the token")
    print("   - Visit http://127.0.0.1:8000/api/ for browsable API")
