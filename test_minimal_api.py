#!/usr/bin/env python
"""
Minimal API test to isolate issues
"""
import os
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'crosssell_project.settings')
django.setup()

from rest_framework import viewsets, serializers
from rest_framework.response import Response
from products.models import Category

class MinimalCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = Category
        fields = ['id', 'name', 'slug']

class MinimalCategoryViewSet(viewsets.ReadOnlyModelViewSet):
    queryset = Category.objects.all()
    serializer_class = MinimalCategorySerializer
    permission_classes = []

def test_minimal_viewset():
    """Test minimal viewset"""
    print("🧪 Testing Minimal ViewSet")
    
    try:
        viewset = MinimalCategoryViewSet()
        queryset = viewset.get_queryset()
        serializer_class = viewset.get_serializer_class()
        
        print(f"✅ ViewSet created successfully")
        print(f"   Queryset: {queryset.count()} items")
        print(f"   Serializer: {serializer_class}")
        
        # Test serialization
        categories = queryset[:1]
        if categories:
            serializer = serializer_class(categories[0])
            print(f"   Sample data: {serializer.data}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    test_minimal_viewset()
