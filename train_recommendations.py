#!/usr/bin/env python
"""
Train recommendation models using sample data
"""
import os
import django
import uuid
from decimal import Decimal
from datetime import timedelta

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'crosssell_project.settings')
django.setup()

from products.models import Product, Order, OrderItem
from recommendations.models import (
    AssociationRule, FrequentItemset, ProductRecommendation, 
    ModelTrainingLog
)
# # Using simple association rule mining instead of MLxtend for now
from collections import defaultdict, Counter
from itertools import combinations
from django.utils import timezone

def prepare_transaction_data():
    """Prepare transaction data for training"""
    print("📊 Preparing Transaction Data...")
    
    # Get all completed orders
    orders = Order.objects.filter(status='completed').prefetch_related('items__product')
    
    transactions = []
    for order in orders:
        # Get product IDs in this transaction
        product_ids = [str(item.product.id) for item in order.items.all()]
        if len(product_ids) > 1:  # Only include multi-item transactions
            transactions.append(product_ids)
    
    print(f"   ✅ Found {len(transactions)} multi-item transactions")
    print(f"   📦 Sample transaction: {transactions[0] if transactions else 'None'}")
    
    return transactions

def train_association_rules(transactions):
    """Train association rules using simple approach"""
    print("\n🧠 Training Association Rules...")

    try:
        print("   🔄 Analyzing transaction patterns...")

        # Calculate item frequencies
        item_counts = Counter()
        total_transactions = len(transactions)

        for transaction in transactions:
            for item in transaction:
                item_counts[item] += 1

        # Find frequent itemsets (items that appear in at least min_support% of transactions)
        min_support = 0.1  # 10% minimum support
        min_support_count = int(min_support * total_transactions)

        frequent_items = {item: count for item, count in item_counts.items()
                         if count >= min_support_count}

        print(f"   ✅ Found {len(frequent_items)} frequent items")

        # Generate association rules
        rules = []
        min_confidence = 0.3

        # For each pair of frequent items, check if they form a good rule
        frequent_item_list = list(frequent_items.keys())

        for i, item1 in enumerate(frequent_item_list):
            for item2 in frequent_item_list[i+1:]:
                # Count co-occurrences
                cooccurrence_count = 0
                item1_count = frequent_items[item1]
                item2_count = frequent_items[item2]

                for transaction in transactions:
                    if item1 in transaction and item2 in transaction:
                        cooccurrence_count += 1

                if cooccurrence_count > 0:
                    # Calculate metrics
                    support = cooccurrence_count / total_transactions
                    confidence1 = cooccurrence_count / item1_count  # item1 -> item2
                    confidence2 = cooccurrence_count / item2_count  # item2 -> item1
                    lift = support / ((item1_count / total_transactions) * (item2_count / total_transactions))

                    # Create rules if confidence is high enough
                    if confidence1 >= min_confidence:
                        rules.append({
                            'antecedent': [item1],
                            'consequent': [item2],
                            'support': support,
                            'confidence': confidence1,
                            'lift': lift
                        })

                    if confidence2 >= min_confidence:
                        rules.append({
                            'antecedent': [item2],
                            'consequent': [item1],
                            'support': support,
                            'confidence': confidence2,
                            'lift': lift
                        })

        print(f"   ✅ Generated {len(rules)} association rules")

        if rules:
            # Save to database
            save_simple_frequent_itemsets(frequent_items, total_transactions)
            save_simple_association_rules(rules)
            return True
        else:
            print("   ⚠️ No rules found with current parameters")
            return False

    except Exception as e:
        print(f"   ❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def save_simple_frequent_itemsets(frequent_items, total_transactions):
    """Save frequent itemsets to database"""
    print("\n💾 Saving Frequent Itemsets...")

    model_version = f"v{timezone.now().strftime('%Y%m%d_%H%M%S')}"
    saved_count = 0

    for item_id, count in frequent_items.items():
        try:
            support = count / total_transactions

            # Create FrequentItemset
            frequent_itemset = FrequentItemset.objects.create(
                support=support,
                itemset_size=1,
                transaction_count=count,
                model_version=model_version
            )

            # Add product
            try:
                product = Product.objects.get(id=item_id)
                frequent_itemset.products.add(product)
                saved_count += 1
            except Product.DoesNotExist:
                print(f"   ⚠️ Product {item_id} not found")
                frequent_itemset.delete()

        except Exception as e:
            print(f"   ⚠️ Error saving itemset: {e}")

    print(f"   ✅ Saved {saved_count} frequent itemsets")

def save_simple_association_rules(rules):
    """Save association rules to database"""
    print("\n💾 Saving Association Rules...")

    model_version = f"v{timezone.now().strftime('%Y%m%d_%H%M%S')}"
    saved_count = 0

    for rule_data in rules:
        try:
            # Get antecedent and consequent product IDs
            antecedent_ids = rule_data['antecedent']
            consequent_ids = rule_data['consequent']

            # Create AssociationRule
            rule = AssociationRule.objects.create(
                support=float(rule_data['support']),
                confidence=float(rule_data['confidence']),
                lift=float(rule_data['lift']),
                conviction=1.0,  # Simplified
                rule_length=len(antecedent_ids) + len(consequent_ids),
                transaction_count=10,  # Simplified
                model_version=model_version,
                is_active=True
            )

            # Add antecedent and consequent products
            try:
                antecedent_products = Product.objects.filter(id__in=antecedent_ids)
                consequent_products = Product.objects.filter(id__in=consequent_ids)

                if antecedent_products.exists() and consequent_products.exists():
                    rule.antecedent.set(antecedent_products)
                    rule.consequent.set(consequent_products)
                    saved_count += 1
                else:
                    rule.delete()

            except Exception as e:
                print(f"   ⚠️ Error setting products for rule: {e}")
                rule.delete()

        except Exception as e:
            print(f"   ⚠️ Error saving rule: {e}")

    print(f"   ✅ Saved {saved_count} association rules")

def generate_product_recommendations():
    """Generate product recommendations from association rules"""
    print("\n🎯 Generating Product Recommendations...")
    
    rules = AssociationRule.objects.filter(is_active=True)
    recommendation_count = 0
    
    for rule in rules:
        try:
            # For each antecedent product, recommend consequent products
            for source_product in rule.antecedent.all():
                for recommended_product in rule.consequent.all():
                    
                    # Avoid self-recommendations
                    if source_product.id == recommended_product.id:
                        continue
                    
                    # Create or update recommendation
                    recommendation, created = ProductRecommendation.objects.get_or_create(
                        source_product=source_product,
                        recommended_product=recommended_product,
                        recommendation_type='cross_sell',
                        defaults={
                            'score': float(rule.confidence),
                            'association_rule': rule,
                            'algorithm_used': 'association_rules',
                            'model_version': rule.model_version,
                            'is_active': True
                        }
                    )
                    
                    if created:
                        recommendation_count += 1
        
        except Exception as e:
            print(f"   ⚠️ Error creating recommendation: {e}")
    
    print(f"   ✅ Generated {recommendation_count} product recommendations")

def create_training_log(success=True, error_message=None):
    """Create training log entry"""
    print("\n📝 Creating Training Log...")

    model_version = f"v{timezone.now().strftime('%Y%m%d_%H%M%S')}"

    try:
        log = ModelTrainingLog.objects.create(
            model_type='apriori',
            model_version=model_version,
            min_support=0.1,
            min_confidence=0.3,
            max_itemsets=100,
            total_transactions=Order.objects.filter(status='completed').count(),
            total_products=Product.objects.count(),
            date_range_start=timezone.now() - timedelta(days=90),
            date_range_end=timezone.now(),
            frequent_itemsets_count=FrequentItemset.objects.count(),
            association_rules_count=AssociationRule.objects.count(),
            recommendations_generated=ProductRecommendation.objects.count(),
            training_duration_seconds=30.0,
            memory_usage_mb=50.0,
            status='completed' if success else 'failed',
            error_message=error_message or '',
            completed_at=timezone.now() if success else None
        )

        print(f"   ✅ Training log created: {log.model_version}")
    except Exception as e:
        print(f"   ⚠️ Could not create training log: {e}")

def test_recommendations():
    """Test the generated recommendations"""
    print("\n🧪 Testing Recommendations...")
    
    # Get a sample product
    sample_product = Product.objects.first()
    if sample_product:
        recommendations = ProductRecommendation.objects.filter(
            source_product=sample_product,
            is_active=True
        )[:3]
        
        print(f"   📱 Recommendations for '{sample_product.name}':")
        for rec in recommendations:
            print(f"      → {rec.recommended_product.name} (score: {rec.score:.2f})")
        
        if not recommendations:
            print("   ⚠️ No recommendations found")
    
    # Show overall stats
    total_rules = AssociationRule.objects.filter(is_active=True).count()
    total_recommendations = ProductRecommendation.objects.filter(is_active=True).count()
    
    print(f"\n📊 Training Results:")
    print(f"   - Association Rules: {total_rules}")
    print(f"   - Product Recommendations: {total_recommendations}")
    print(f"   - Frequent Itemsets: {FrequentItemset.objects.count()}")

def main():
    """Main training function"""
    print("🤖 Training Recommendation Models")
    print("=" * 50)
    
    try:
        # Step 1: Prepare data
        transactions = prepare_transaction_data()
        
        if not transactions:
            print("❌ No transaction data found!")
            return
        
        # Step 2: Train models
        success = train_association_rules(transactions)
        
        if success:
            # Step 3: Generate recommendations
            generate_product_recommendations()
            
            # Step 4: Create log
            create_training_log(success=True)
            
            # Step 5: Test results
            test_recommendations()
            
            print("\n" + "=" * 50)
            print("✅ Model Training Complete!")
            print("🎯 Recommendations are now available via API!")
            
        else:
            create_training_log(success=False, error_message="No rules generated")
            print("\n❌ Training failed - no rules generated")
    
    except Exception as e:
        print(f"\n❌ Training failed with error: {e}")
        create_training_log(success=False, error_message=str(e))

if __name__ == '__main__':
    main()
