#!/usr/bin/env python
"""
Train recommendation models using sample data
"""
import os
import django
import uuid
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'crosssell_project.settings')
django.setup()

from products.models import Product, Order, OrderItem
from recommendations.models import (
    AssociationRule, FrequentItemset, ProductRecommendation, 
    ModelTrainingLog
)
# from recommendations.ml_engine import MLxtendCrossSellEngine
from django.utils import timezone

def prepare_transaction_data():
    """Prepare transaction data for training"""
    print("📊 Preparing Transaction Data...")
    
    # Get all completed orders
    orders = Order.objects.filter(status='completed').prefetch_related('items__product')
    
    transactions = []
    for order in orders:
        # Get product IDs in this transaction
        product_ids = [str(item.product.id) for item in order.items.all()]
        if len(product_ids) > 1:  # Only include multi-item transactions
            transactions.append(product_ids)
    
    print(f"   ✅ Found {len(transactions)} multi-item transactions")
    print(f"   📦 Sample transaction: {transactions[0] if transactions else 'None'}")
    
    return transactions

def train_association_rules(transactions):
    """Train association rules using MLxtend"""
    print("\n🧠 Training Association Rules...")
    
    try:
        # Initialize ML engine
        engine = MLxtendCrossSellEngine()
        
        # Train the model
        print("   🔄 Training model with MLxtend...")
        results = engine.train_model(
            transactions=transactions,
            min_support=0.1,  # Lower threshold for small dataset
            min_confidence=0.3,
            max_itemsets=100
        )
        
        if results and 'frequent_itemsets' in results and 'association_rules' in results:
            frequent_itemsets = results['frequent_itemsets']
            association_rules = results['association_rules']
            
            print(f"   ✅ Found {len(frequent_itemsets)} frequent itemsets")
            print(f"   ✅ Found {len(association_rules)} association rules")
            
            # Save to database
            save_frequent_itemsets(frequent_itemsets)
            save_association_rules(association_rules)
            
            return True
        else:
            print("   ⚠️ No rules found with current parameters")
            return False
            
    except Exception as e:
        print(f"   ❌ Training failed: {e}")
        return False

def save_frequent_itemsets(itemsets_df):
    """Save frequent itemsets to database"""
    print("\n💾 Saving Frequent Itemsets...")
    
    model_version = f"v{timezone.now().strftime('%Y%m%d_%H%M%S')}"
    saved_count = 0
    
    for _, row in itemsets_df.iterrows():
        try:
            # Get product IDs from itemsets
            itemset = row['itemsets']
            product_ids = list(itemset)
            
            # Create FrequentItemset
            frequent_itemset = FrequentItemset.objects.create(
                support=float(row['support']),
                itemset_size=len(product_ids),
                transaction_count=len(product_ids),  # Simplified
                model_version=model_version
            )
            
            # Add products
            products = Product.objects.filter(id__in=product_ids)
            frequent_itemset.products.set(products)
            
            saved_count += 1
            
        except Exception as e:
            print(f"   ⚠️ Error saving itemset: {e}")
    
    print(f"   ✅ Saved {saved_count} frequent itemsets")

def save_association_rules(rules_df):
    """Save association rules to database"""
    print("\n💾 Saving Association Rules...")
    
    model_version = f"v{timezone.now().strftime('%Y%m%d_%H%M%S')}"
    saved_count = 0
    
    for _, row in rules_df.iterrows():
        try:
            # Get antecedent and consequent product IDs
            antecedent_ids = list(row['antecedents'])
            consequent_ids = list(row['consequents'])
            
            # Create AssociationRule
            rule = AssociationRule.objects.create(
                support=float(row['support']),
                confidence=float(row['confidence']),
                lift=float(row['lift']),
                conviction=float(row.get('conviction', 1.0)),
                rule_length=len(antecedent_ids) + len(consequent_ids),
                transaction_count=10,  # Simplified
                model_version=model_version,
                is_active=True
            )
            
            # Add antecedent and consequent products
            antecedent_products = Product.objects.filter(id__in=antecedent_ids)
            consequent_products = Product.objects.filter(id__in=consequent_ids)
            
            rule.antecedent.set(antecedent_products)
            rule.consequent.set(consequent_products)
            
            saved_count += 1
            
        except Exception as e:
            print(f"   ⚠️ Error saving rule: {e}")
    
    print(f"   ✅ Saved {saved_count} association rules")

def generate_product_recommendations():
    """Generate product recommendations from association rules"""
    print("\n🎯 Generating Product Recommendations...")
    
    rules = AssociationRule.objects.filter(is_active=True)
    recommendation_count = 0
    
    for rule in rules:
        try:
            # For each antecedent product, recommend consequent products
            for source_product in rule.antecedent.all():
                for recommended_product in rule.consequent.all():
                    
                    # Avoid self-recommendations
                    if source_product.id == recommended_product.id:
                        continue
                    
                    # Create or update recommendation
                    recommendation, created = ProductRecommendation.objects.get_or_create(
                        source_product=source_product,
                        recommended_product=recommended_product,
                        defaults={
                            'recommendation_type': 'cross_sell',
                            'score': float(rule.confidence),
                            'confidence': float(rule.confidence),
                            'support': float(rule.support),
                            'lift': float(rule.lift),
                            'algorithm_used': 'association_rules',
                            'model_version': rule.model_version,
                            'is_active': True
                        }
                    )
                    
                    if created:
                        recommendation_count += 1
        
        except Exception as e:
            print(f"   ⚠️ Error creating recommendation: {e}")
    
    print(f"   ✅ Generated {recommendation_count} product recommendations")

def create_training_log(success=True, error_message=None):
    """Create training log entry"""
    print("\n📝 Creating Training Log...")
    
    model_version = f"v{timezone.now().strftime('%Y%m%d_%H%M%S')}"
    
    log = ModelTrainingLog.objects.create(
        model_version=model_version,
        algorithm='mlxtend_apriori',
        parameters={
            'min_support': 0.1,
            'min_confidence': 0.3,
            'max_itemsets': 100
        },
        status='completed' if success else 'failed',
        training_duration=30.0,  # Simplified
        data_size=Order.objects.filter(status='completed').count(),
        frequent_itemsets_count=FrequentItemset.objects.count(),
        association_rules_count=AssociationRule.objects.count(),
        error_message=error_message,
        completed_at=timezone.now() if success else None
    )
    
    print(f"   ✅ Training log created: {log.model_version}")

def test_recommendations():
    """Test the generated recommendations"""
    print("\n🧪 Testing Recommendations...")
    
    # Get a sample product
    sample_product = Product.objects.first()
    if sample_product:
        recommendations = ProductRecommendation.objects.filter(
            source_product=sample_product,
            is_active=True
        )[:3]
        
        print(f"   📱 Recommendations for '{sample_product.name}':")
        for rec in recommendations:
            print(f"      → {rec.recommended_product.name} (confidence: {rec.confidence:.2f})")
        
        if not recommendations:
            print("   ⚠️ No recommendations found")
    
    # Show overall stats
    total_rules = AssociationRule.objects.filter(is_active=True).count()
    total_recommendations = ProductRecommendation.objects.filter(is_active=True).count()
    
    print(f"\n📊 Training Results:")
    print(f"   - Association Rules: {total_rules}")
    print(f"   - Product Recommendations: {total_recommendations}")
    print(f"   - Frequent Itemsets: {FrequentItemset.objects.count()}")

def main():
    """Main training function"""
    print("🤖 Training Recommendation Models")
    print("=" * 50)
    
    try:
        # Step 1: Prepare data
        transactions = prepare_transaction_data()
        
        if not transactions:
            print("❌ No transaction data found!")
            return
        
        # Step 2: Train models
        success = train_association_rules(transactions)
        
        if success:
            # Step 3: Generate recommendations
            generate_product_recommendations()
            
            # Step 4: Create log
            create_training_log(success=True)
            
            # Step 5: Test results
            test_recommendations()
            
            print("\n" + "=" * 50)
            print("✅ Model Training Complete!")
            print("🎯 Recommendations are now available via API!")
            
        else:
            create_training_log(success=False, error_message="No rules generated")
            print("\n❌ Training failed - no rules generated")
    
    except Exception as e:
        print(f"\n❌ Training failed with error: {e}")
        create_training_log(success=False, error_message=str(e))

if __name__ == '__main__':
    main()
